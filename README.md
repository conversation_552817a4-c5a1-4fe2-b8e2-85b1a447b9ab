# ZENRAT

A remote administration tool with a modern web interface.

## Components

### Server (Control Panel)
- FastAPI web application
- Real-time client monitoring and control
- Live screen streaming
- Remote file management
- Command execution

### Client
- Cross-platform Python client
- Screen capture and streaming
- File system operations
- Command execution

## Setup

### Server Setup
1. Navigate to the server directory
2. Install dependencies: `pip install -r requirements.txt`
3. Run the server: `python app.py`

### Client Setup
1. Navigate to the client directory
2. Install dependencies: `pip install -r requirements.txt`
3. Run the client: `python client.py`

## Security Notice
This tool is intended for educational purposes and legitimate system administration only.
