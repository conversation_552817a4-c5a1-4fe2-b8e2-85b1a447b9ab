"""
PepeRAT Security Module - Cryptographic Functions
Provides end-to-end encryption using RSA + AES hybrid encryption
"""

import os
import base64
import json
import logging
from typing import <PERSON><PERSON>, Dict, Optional
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad, unpad

logger = logging.getLogger(__name__)

class CryptoManager:
    """Handles all cryptographic operations for PepeRAT"""
    
    def __init__(self):
        self.backend = default_backend()
        self.rsa_key_size = 2048
        self.aes_key_size = 32  # 256 bits
        self.iv_size = 16  # 128 bits
        
    def generate_rsa_keypair(self) -> <PERSON><PERSON>[bytes, bytes]:
        """Generate RSA public/private key pair"""
        try:
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=self.rsa_key_size,
                backend=self.backend
            )
            
            public_key = private_key.public_key()
            
            # Serialize keys
            private_pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            
            public_pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            logger.info("RSA keypair generated successfully")
            return private_pem, public_pem
            
        except Exception as e:
            logger.error(f"Error generating RSA keypair: {str(e)}")
            raise
    
    def load_rsa_key(self, key_data: bytes, is_private: bool = True):
        """Load RSA key from PEM data"""
        try:
            if is_private:
                return serialization.load_pem_private_key(
                    key_data, password=None, backend=self.backend
                )
            else:
                return serialization.load_pem_public_key(
                    key_data, backend=self.backend
                )
        except Exception as e:
            logger.error(f"Error loading RSA key: {str(e)}")
            raise
    
    def generate_aes_key(self) -> bytes:
        """Generate random AES key"""
        return get_random_bytes(self.aes_key_size)
    
    def encrypt_aes(self, data: bytes, key: bytes) -> Dict[str, str]:
        """Encrypt data using AES-256-CBC"""
        try:
            # Generate random IV
            iv = get_random_bytes(self.iv_size)
            
            # Create cipher
            cipher = AES.new(key, AES.MODE_CBC, iv)
            
            # Pad data and encrypt
            padded_data = pad(data, AES.block_size)
            encrypted_data = cipher.encrypt(padded_data)
            
            return {
                'data': base64.b64encode(encrypted_data).decode('utf-8'),
                'iv': base64.b64encode(iv).decode('utf-8')
            }
            
        except Exception as e:
            logger.error(f"Error encrypting with AES: {str(e)}")
            raise
    
    def decrypt_aes(self, encrypted_data: str, key: bytes, iv: str) -> bytes:
        """Decrypt data using AES-256-CBC"""
        try:
            # Decode base64
            encrypted_bytes = base64.b64decode(encrypted_data)
            iv_bytes = base64.b64decode(iv)
            
            # Create cipher
            cipher = AES.new(key, AES.MODE_CBC, iv_bytes)
            
            # Decrypt and unpad
            decrypted_padded = cipher.decrypt(encrypted_bytes)
            decrypted_data = unpad(decrypted_padded, AES.block_size)
            
            return decrypted_data
            
        except Exception as e:
            logger.error(f"Error decrypting with AES: {str(e)}")
            raise
    
    def encrypt_rsa(self, data: bytes, public_key) -> str:
        """Encrypt data using RSA public key"""
        try:
            encrypted = public_key.encrypt(
                data,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            return base64.b64encode(encrypted).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error encrypting with RSA: {str(e)}")
            raise
    
    def decrypt_rsa(self, encrypted_data: str, private_key) -> bytes:
        """Decrypt data using RSA private key"""
        try:
            encrypted_bytes = base64.b64decode(encrypted_data)
            decrypted = private_key.decrypt(
                encrypted_bytes,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            return decrypted
            
        except Exception as e:
            logger.error(f"Error decrypting with RSA: {str(e)}")
            raise
    
    def hybrid_encrypt(self, data: bytes, public_key) -> Dict[str, str]:
        """
        Hybrid encryption: RSA for key exchange, AES for data
        Returns encrypted data and RSA-encrypted AES key
        """
        try:
            # Generate AES key
            aes_key = self.generate_aes_key()
            
            # Encrypt data with AES
            aes_encrypted = self.encrypt_aes(data, aes_key)
            
            # Encrypt AES key with RSA
            rsa_encrypted_key = self.encrypt_rsa(aes_key, public_key)
            
            return {
                'data': aes_encrypted['data'],
                'iv': aes_encrypted['iv'],
                'key': rsa_encrypted_key
            }
            
        except Exception as e:
            logger.error(f"Error in hybrid encryption: {str(e)}")
            raise
    
    def hybrid_decrypt(self, encrypted_package: Dict[str, str], private_key) -> bytes:
        """
        Hybrid decryption: RSA for key recovery, AES for data
        """
        try:
            # Decrypt AES key with RSA
            aes_key = self.decrypt_rsa(encrypted_package['key'], private_key)
            
            # Decrypt data with AES
            decrypted_data = self.decrypt_aes(
                encrypted_package['data'],
                aes_key,
                encrypted_package['iv']
            )
            
            return decrypted_data
            
        except Exception as e:
            logger.error(f"Error in hybrid decryption: {str(e)}")
            raise
    
    def create_secure_message(self, message: Dict, public_key) -> str:
        """Create encrypted message package"""
        try:
            # Convert message to JSON bytes
            message_bytes = json.dumps(message).encode('utf-8')
            
            # Encrypt using hybrid method
            encrypted_package = self.hybrid_encrypt(message_bytes, public_key)
            
            # Return base64 encoded package
            return base64.b64encode(
                json.dumps(encrypted_package).encode('utf-8')
            ).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error creating secure message: {str(e)}")
            raise
    
    def decrypt_secure_message(self, encrypted_message: str, private_key) -> Dict:
        """Decrypt secure message package"""
        try:
            # Decode base64 and parse JSON
            package_bytes = base64.b64decode(encrypted_message)
            encrypted_package = json.loads(package_bytes.decode('utf-8'))
            
            # Decrypt using hybrid method
            decrypted_bytes = self.hybrid_decrypt(encrypted_package, private_key)
            
            # Parse JSON message
            return json.loads(decrypted_bytes.decode('utf-8'))
            
        except Exception as e:
            logger.error(f"Error decrypting secure message: {str(e)}")
            raise

# Global crypto manager instance
crypto_manager = CryptoManager()
