"""
PepeRAT Security Module - Key Management
Handles RSA key generation, storage, and client key exchange
"""

import os
import json
import logging
from typing import Dict, Optional, Tuple
from pathlib import Path
from .crypto import crypto_manager

logger = logging.getLogger(__name__)

class KeyManager:
    """Manages cryptographic keys for PepeRAT server and clients"""
    
    def __init__(self, keys_dir: str = "keys"):
        self.keys_dir = Path(keys_dir)
        self.keys_dir.mkdir(exist_ok=True)
        
        # Server key files
        self.server_private_key_file = self.keys_dir / "server_private.pem"
        self.server_public_key_file = self.keys_dir / "server_public.pem"
        
        # Client keys storage
        self.client_keys_file = self.keys_dir / "client_keys.json"
        
        # Initialize server keys
        self.server_private_key = None
        self.server_public_key = None
        self.client_keys = {}
        
        self._load_or_generate_server_keys()
        self._load_client_keys()
    
    def _load_or_generate_server_keys(self):
        """Load existing server keys or generate new ones"""
        try:
            if self.server_private_key_file.exists() and self.server_public_key_file.exists():
                # Load existing keys
                with open(self.server_private_key_file, 'rb') as f:
                    private_key_data = f.read()
                
                with open(self.server_public_key_file, 'rb') as f:
                    public_key_data = f.read()
                
                self.server_private_key = crypto_manager.load_rsa_key(private_key_data, is_private=True)
                self.server_public_key = crypto_manager.load_rsa_key(public_key_data, is_private=False)
                
                logger.info("Server keys loaded successfully")
            else:
                # Generate new keys
                self._generate_server_keys()
                
        except Exception as e:
            logger.error(f"Error loading server keys: {str(e)}")
            # Try to generate new keys
            self._generate_server_keys()
    
    def _generate_server_keys(self):
        """Generate new server RSA key pair"""
        try:
            logger.info("Generating new server RSA key pair...")
            
            private_key_data, public_key_data = crypto_manager.generate_rsa_keypair()
            
            # Save keys to files
            with open(self.server_private_key_file, 'wb') as f:
                f.write(private_key_data)
            
            with open(self.server_public_key_file, 'wb') as f:
                f.write(public_key_data)
            
            # Set restrictive permissions
            os.chmod(self.server_private_key_file, 0o600)
            os.chmod(self.server_public_key_file, 0o644)
            
            # Load the keys
            self.server_private_key = crypto_manager.load_rsa_key(private_key_data, is_private=True)
            self.server_public_key = crypto_manager.load_rsa_key(public_key_data, is_private=False)
            
            logger.info("Server keys generated and saved successfully")
            
        except Exception as e:
            logger.error(f"Error generating server keys: {str(e)}")
            raise
    
    def _load_client_keys(self):
        """Load client public keys from storage"""
        try:
            if self.client_keys_file.exists():
                with open(self.client_keys_file, 'r') as f:
                    client_keys_data = json.load(f)
                
                # Load each client's public key
                for client_id, key_data in client_keys_data.items():
                    try:
                        public_key_bytes = key_data['public_key'].encode('utf-8')
                        public_key = crypto_manager.load_rsa_key(public_key_bytes, is_private=False)
                        
                        self.client_keys[client_id] = {
                            'public_key': public_key,
                            'registered_at': key_data.get('registered_at'),
                            'last_used': key_data.get('last_used')
                        }
                    except Exception as e:
                        logger.warning(f"Failed to load key for client {client_id}: {str(e)}")
                
                logger.info(f"Loaded {len(self.client_keys)} client keys")
            else:
                logger.info("No existing client keys found")
                
        except Exception as e:
            logger.error(f"Error loading client keys: {str(e)}")
            self.client_keys = {}
    
    def _save_client_keys(self):
        """Save client keys to storage"""
        try:
            client_keys_data = {}
            
            for client_id, key_info in self.client_keys.items():
                # Serialize public key
                public_key_bytes = key_info['public_key'].public_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PublicFormat.SubjectPublicKeyInfo
                )
                
                client_keys_data[client_id] = {
                    'public_key': public_key_bytes.decode('utf-8'),
                    'registered_at': key_info.get('registered_at'),
                    'last_used': key_info.get('last_used')
                }
            
            with open(self.client_keys_file, 'w') as f:
                json.dump(client_keys_data, f, indent=2)
            
            logger.debug(f"Saved {len(client_keys_data)} client keys")
            
        except Exception as e:
            logger.error(f"Error saving client keys: {str(e)}")
    
    def get_server_public_key_pem(self) -> str:
        """Get server public key in PEM format"""
        try:
            from cryptography.hazmat.primitives import serialization
            
            public_key_bytes = self.server_public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            return public_key_bytes.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error getting server public key: {str(e)}")
            raise
    
    def register_client_key(self, client_id: str, public_key_pem: str) -> bool:
        """Register a client's public key"""
        try:
            # Load and validate the public key
            public_key_bytes = public_key_pem.encode('utf-8')
            public_key = crypto_manager.load_rsa_key(public_key_bytes, is_private=False)
            
            # Store the key
            from datetime import datetime
            self.client_keys[client_id] = {
                'public_key': public_key,
                'registered_at': datetime.now().isoformat(),
                'last_used': datetime.now().isoformat()
            }
            
            # Save to disk
            self._save_client_keys()
            
            logger.info(f"Registered public key for client {client_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering client key: {str(e)}")
            return False
    
    def get_client_public_key(self, client_id: str):
        """Get a client's public key"""
        return self.client_keys.get(client_id, {}).get('public_key')
    
    def update_client_last_used(self, client_id: str):
        """Update client's last used timestamp"""
        if client_id in self.client_keys:
            from datetime import datetime
            self.client_keys[client_id]['last_used'] = datetime.now().isoformat()
            self._save_client_keys()
    
    def remove_client_key(self, client_id: str) -> bool:
        """Remove a client's key"""
        try:
            if client_id in self.client_keys:
                del self.client_keys[client_id]
                self._save_client_keys()
                logger.info(f"Removed key for client {client_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error removing client key: {str(e)}")
            return False
    
    def encrypt_for_client(self, client_id: str, message: Dict) -> Optional[str]:
        """Encrypt a message for a specific client"""
        try:
            client_public_key = self.get_client_public_key(client_id)
            if not client_public_key:
                logger.warning(f"No public key found for client {client_id}")
                return None
            
            encrypted_message = crypto_manager.create_secure_message(message, client_public_key)
            self.update_client_last_used(client_id)
            
            return encrypted_message
            
        except Exception as e:
            logger.error(f"Error encrypting message for client {client_id}: {str(e)}")
            return None
    
    def decrypt_from_client(self, client_id: str, encrypted_message: str) -> Optional[Dict]:
        """Decrypt a message from a client"""
        try:
            decrypted_message = crypto_manager.decrypt_secure_message(
                encrypted_message, 
                self.server_private_key
            )
            
            self.update_client_last_used(client_id)
            return decrypted_message
            
        except Exception as e:
            logger.error(f"Error decrypting message from client {client_id}: {str(e)}")
            return None
    
    def list_client_keys(self) -> Dict[str, Dict]:
        """List all registered client keys with metadata"""
        result = {}
        for client_id, key_info in self.client_keys.items():
            result[client_id] = {
                'registered_at': key_info.get('registered_at'),
                'last_used': key_info.get('last_used')
            }
        return result

# Global key manager instance
key_manager = KeyManager()
