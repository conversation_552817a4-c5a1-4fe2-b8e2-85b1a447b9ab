"""
PepeRAT Client Security Module - Cryptographic Functions
Client-side encryption and key management
"""

import os
import base64
import json
import logging
from typing import <PERSON><PERSON>, Dict, Optional
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.backends import default_backend
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad, unpad

logger = logging.getLogger(__name__)

class ClientCryptoManager:
    """Handles cryptographic operations for PepeRAT client"""
    
    def __init__(self):
        self.backend = default_backend()
        self.rsa_key_size = 2048
        self.aes_key_size = 32  # 256 bits
        self.iv_size = 16  # 128 bits
        
        # Client keys
        self.private_key = None
        self.public_key = None
        self.server_public_key = None
        
        # Key files
        self.private_key_file = "client_private.pem"
        self.public_key_file = "client_public.pem"
        self.server_key_file = "server_public.pem"
        
        self._load_or_generate_keys()
    
    def _load_or_generate_keys(self):
        """Load existing keys or generate new ones"""
        try:
            if os.path.exists(self.private_key_file) and os.path.exists(self.public_key_file):
                # Load existing keys
                with open(self.private_key_file, 'rb') as f:
                    private_key_data = f.read()
                
                with open(self.public_key_file, 'rb') as f:
                    public_key_data = f.read()
                
                self.private_key = self._load_rsa_key(private_key_data, is_private=True)
                self.public_key = self._load_rsa_key(public_key_data, is_private=False)
                
                logger.info("Client keys loaded successfully")
            else:
                # Generate new keys
                self._generate_keys()
            
            # Load server public key if available
            if os.path.exists(self.server_key_file):
                with open(self.server_key_file, 'rb') as f:
                    server_key_data = f.read()
                self.server_public_key = self._load_rsa_key(server_key_data, is_private=False)
                logger.info("Server public key loaded")
                
        except Exception as e:
            logger.error(f"Error loading client keys: {str(e)}")
            self._generate_keys()
    
    def _generate_keys(self):
        """Generate new client RSA key pair"""
        try:
            logger.info("Generating new client RSA key pair...")
            
            private_key_data, public_key_data = self._generate_rsa_keypair()
            
            # Save keys to files
            with open(self.private_key_file, 'wb') as f:
                f.write(private_key_data)
            
            with open(self.public_key_file, 'wb') as f:
                f.write(public_key_data)
            
            # Set restrictive permissions
            try:
                os.chmod(self.private_key_file, 0o600)
                os.chmod(self.public_key_file, 0o644)
            except:
                pass  # Windows doesn't support chmod
            
            # Load the keys
            self.private_key = self._load_rsa_key(private_key_data, is_private=True)
            self.public_key = self._load_rsa_key(public_key_data, is_private=False)
            
            logger.info("Client keys generated and saved successfully")
            
        except Exception as e:
            logger.error(f"Error generating client keys: {str(e)}")
            raise
    
    def _generate_rsa_keypair(self) -> Tuple[bytes, bytes]:
        """Generate RSA public/private key pair"""
        try:
            private_key = rsa.generate_private_key(
                public_exponent=65537,
                key_size=self.rsa_key_size,
                backend=self.backend
            )
            
            public_key = private_key.public_key()
            
            # Serialize keys
            private_pem = private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            )
            
            public_pem = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            return private_pem, public_pem
            
        except Exception as e:
            logger.error(f"Error generating RSA keypair: {str(e)}")
            raise
    
    def _load_rsa_key(self, key_data: bytes, is_private: bool = True):
        """Load RSA key from PEM data"""
        try:
            if is_private:
                return serialization.load_pem_private_key(
                    key_data, password=None, backend=self.backend
                )
            else:
                return serialization.load_pem_public_key(
                    key_data, backend=self.backend
                )
        except Exception as e:
            logger.error(f"Error loading RSA key: {str(e)}")
            raise
    
    def set_server_public_key(self, server_key_pem: str):
        """Set and save server public key"""
        try:
            server_key_bytes = server_key_pem.encode('utf-8')
            self.server_public_key = self._load_rsa_key(server_key_bytes, is_private=False)
            
            # Save to file
            with open(self.server_key_file, 'wb') as f:
                f.write(server_key_bytes)
            
            logger.info("Server public key set and saved")
            
        except Exception as e:
            logger.error(f"Error setting server public key: {str(e)}")
            raise
    
    def get_public_key_pem(self) -> str:
        """Get client public key in PEM format"""
        try:
            public_key_bytes = self.public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
            return public_key_bytes.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error getting public key: {str(e)}")
            raise
    
    def encrypt_for_server(self, message: Dict) -> Optional[str]:
        """Encrypt a message for the server"""
        try:
            if not self.server_public_key:
                logger.warning("No server public key available")
                return None
            
            # Convert message to JSON bytes
            message_bytes = json.dumps(message).encode('utf-8')
            
            # Generate AES key
            aes_key = get_random_bytes(self.aes_key_size)
            
            # Encrypt data with AES
            iv = get_random_bytes(self.iv_size)
            cipher = AES.new(aes_key, AES.MODE_CBC, iv)
            padded_data = pad(message_bytes, AES.block_size)
            encrypted_data = cipher.encrypt(padded_data)
            
            # Encrypt AES key with RSA
            encrypted_key = self.server_public_key.encrypt(
                aes_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Create encrypted package
            encrypted_package = {
                'data': base64.b64encode(encrypted_data).decode('utf-8'),
                'iv': base64.b64encode(iv).decode('utf-8'),
                'key': base64.b64encode(encrypted_key).decode('utf-8')
            }
            
            # Return base64 encoded package
            return base64.b64encode(
                json.dumps(encrypted_package).encode('utf-8')
            ).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error encrypting message for server: {str(e)}")
            return None
    
    def decrypt_from_server(self, encrypted_message: str) -> Optional[Dict]:
        """Decrypt a message from the server"""
        try:
            # Decode base64 and parse JSON
            package_bytes = base64.b64decode(encrypted_message)
            encrypted_package = json.loads(package_bytes.decode('utf-8'))
            
            # Decrypt AES key with RSA
            encrypted_key = base64.b64decode(encrypted_package['key'])
            aes_key = self.private_key.decrypt(
                encrypted_key,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            
            # Decrypt data with AES
            encrypted_data = base64.b64decode(encrypted_package['data'])
            iv = base64.b64decode(encrypted_package['iv'])
            
            cipher = AES.new(aes_key, AES.MODE_CBC, iv)
            decrypted_padded = cipher.decrypt(encrypted_data)
            decrypted_data = unpad(decrypted_padded, AES.block_size)
            
            # Parse JSON message
            return json.loads(decrypted_data.decode('utf-8'))
            
        except Exception as e:
            logger.error(f"Error decrypting message from server: {str(e)}")
            return None

# Global client crypto manager instance
client_crypto_manager = ClientCryptoManager()
