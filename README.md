# PepeRAT - Advanced Remote Administration Tool v2.0

![PepeRAT](https://img.shields.io/badge/PepeRAT-v2.0.0-green?style=for-the-badge) ![Python](https://img.shields.io/badge/Python-3.8+-blue?style=for-the-badge) ![License](https://img.shields.io/badge/License-Educational-yellow?style=for-the-badge)

**PepeRAT** is a next-generation remote administration tool built with modern security practices, comprehensive encryption, and advanced features. This represents a complete evolution from ZENRAT with end-to-end encryption, modular architecture, and extensive security hardening.

## 🚀 Key Features

### 🔐 Advanced Security
- **End-to-End Encryption**: RSA-2048 + AES-256 hybrid encryption
- **Secure Key Exchange**: Automatic cryptographic key management
- **Input Sanitization**: Protection against injection attacks
- **Sandboxed Execution**: Secure command execution environment
- **Encrypted Logging**: All activities logged with encryption

### 🖥️ Remote Control
- **Live Screen Streaming**: Real-time screen capture with quality control
- **Interactive Control**: Full mouse and keyboard remote control
- **Multi-Monitor Support**: Capture from multiple displays
- **File Management**: Secure file upload/download with validation
- **System Profiling**: Comprehensive system information gathering

### 🎯 Advanced Capabilities
- **Webcam Access**: Remote camera capture and streaming
- **Microphone Recording**: Audio capture and transmission
- **Clipboard Management**: Read/write clipboard content
- **Registry Access**: Windows registry manipulation (Windows only)
- **Persistence Management**: Multiple persistence methods across platforms
- **Self-Destruct**: Secure client removal and cleanup

### 🛠️ Client Builder
- **Web-Based Builder**: Generate custom clients through intuitive interface
- **Cross-Platform**: Windows, Linux, and macOS client generation
- **Feature Selection**: Enable/disable specific capabilities per build
- **Executable Compilation**: PyInstaller integration for standalone executables

## 🏗️ Architecture

```
PepeRAT/
├── server/                    # FastAPI-based server
│   ├── peperat_server.py      # Main server application
│   ├── client_builder.py      # Client generation system
│   ├── security/              # Security modules
│   └── templates/             # Web interface templates
├── client/                    # Python client application
│   ├── peperat_client.py      # Main client application
│   ├── security/              # Client-side security
│   └── modules/               # Feature modules
└── README.md
```

## 🔧 Installation & Setup

### Server Installation
```bash
git clone https://github.com/your-repo/PepeRAT.git
cd PepeRAT/server
pip install -r requirements.txt
python peperat_server.py
```
Access dashboard at `http://localhost:8000`

### Client Installation
```bash
cd client
pip install -r requirements.txt
python peperat_client.py
```

## 🎮 Usage Guide

### Dashboard Features
- **Real-time Monitoring**: Live client status and statistics
- **Multi-Client Management**: Handle multiple connections simultaneously
- **Interactive Remote Control**: Full desktop control with mouse/keyboard
- **File Browser**: Navigate and manage remote file systems
- **System Information**: Detailed hardware and software profiling

### Client Builder
1. Navigate to `/builder` in the web interface
2. Configure client settings (server URL, features, security options)
3. Select target platform (Windows/Linux/macOS)
4. Build and download custom client executable

## 🔒 Security Implementation

### Encryption Stack
- **RSA-2048**: Asymmetric encryption for secure key exchange
- **AES-256-CBC**: Symmetric encryption for data transmission
- **HMAC-SHA256**: Message authentication and integrity
- **Secure Random**: Cryptographically secure random number generation

### Security Hardening
- **Input Validation**: All inputs sanitized and validated
- **Command Injection Protection**: Sandboxed execution environment
- **Path Traversal Prevention**: File operations restricted to safe paths
- **Resource Limits**: CPU and memory usage restrictions
- **Audit Logging**: Comprehensive activity logging with encryption

## 🌐 Platform Support

### Server Platforms
- **Linux**: Ubuntu 18.04+, CentOS 7+, Debian 9+
- **Windows**: Windows 10+, Windows Server 2016+
- **macOS**: macOS 10.14+

### Client Platforms
- **Windows**: Windows 7+, Windows Server 2008+
- **Linux**: Most distributions with Python 3.7+
- **macOS**: macOS 10.12+

## 📋 System Requirements

### Server Requirements
- Python 3.8+
- 2GB RAM minimum (4GB recommended)
- 10GB disk space
- Network connectivity

### Client Requirements
- Python 3.7+
- 512MB RAM minimum
- Network connectivity

## ⚠️ Legal & Ethical Use

**CRITICAL NOTICE**: This tool is provided exclusively for educational purposes and authorized security testing.

### ✅ Authorized Use Cases
- Educational research and cybersecurity learning
- Authorized penetration testing with proper documentation
- Security research with explicit permission
- System administration of owned infrastructure

### ❌ Prohibited Activities
- Unauthorized access to computer systems
- Malicious activities or data theft
- Violation of privacy laws and regulations
- Any illegal or unethical activities

## 🤝 Contributing

We welcome contributions! Please:
1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request with detailed description

## 📄 License

This project is licensed under the MIT License for educational purposes.

## 🆘 Support

For support:
- Create an issue on GitHub
- Review documentation and security guidelines
- Check the troubleshooting section

---

**Remember**: This tool should only be used on systems you own or have explicit written permission to test. Always follow responsible disclosure practices and respect privacy and legal boundaries.
