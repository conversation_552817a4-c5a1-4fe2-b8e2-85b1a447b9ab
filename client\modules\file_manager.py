import os
import shutil
import logging
from pathlib import Path
from datetime import datetime

logger = logging.getLogger(__name__)

class FileManager:
    def __init__(self):
        """Initialize the file manager module."""
        logger.info("File manager module initialized")
    
    def list_directory(self, path="."):
        """
        List files and directories in the specified path.
        
        Args:
            path: The directory path to list
        
        Returns:
            list: A list of dictionaries containing file information
        """
        try:
            # Convert to absolute path
            abs_path = os.path.abspath(path)
            
            # Check if the path exists
            if not os.path.exists(abs_path):
                logger.error(f"Path does not exist: {abs_path}")
                return []
            
            # Get the list of files and directories
            items = []
            for item in os.listdir(abs_path):
                item_path = os.path.join(abs_path, item)
                item_stat = os.stat(item_path)
                
                # Create item info
                item_info = {
                    "name": item,
                    "path": item_path,
                    "is_dir": os.path.isdir(item_path),
                    "size": item_stat.st_size,
                    "modified": datetime.fromtimestamp(item_stat.st_mtime).isoformat(),
                    "created": datetime.fromtimestamp(item_stat.st_ctime).isoformat(),
                    "permissions": oct(item_stat.st_mode)[-3:]
                }
                
                items.append(item_info)
            
            logger.info(f"Listed directory: {abs_path} ({len(items)} items)")
            return items
        
        except Exception as e:
            logger.error(f"Error listing directory {path}: {str(e)}")
            return []
    
    def read_file(self, path):
        """
        Read a file and return its contents.
        
        Args:
            path: The file path to read
        
        Returns:
            bytes: The file contents as bytes, or None if an error occurred
        """
        try:
            # Convert to absolute path
            abs_path = os.path.abspath(path)
            
            # Check if the file exists
            if not os.path.exists(abs_path) or not os.path.isfile(abs_path):
                logger.error(f"File does not exist: {abs_path}")
                return None
            
            # Read the file
            with open(abs_path, "rb") as f:
                data = f.read()
            
            logger.info(f"Read file: {abs_path} ({len(data)} bytes)")
            return data
        
        except Exception as e:
            logger.error(f"Error reading file {path}: {str(e)}")
            return None
    
    def write_file(self, path, data):
        """
        Write data to a file.
        
        Args:
            path: The file path to write to
            data: The data to write (bytes)
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert to absolute path
            abs_path = os.path.abspath(path)
            
            # Create directory if it doesn't exist
            os.makedirs(os.path.dirname(abs_path), exist_ok=True)
            
            # Write the file
            with open(abs_path, "wb") as f:
                f.write(data)
            
            logger.info(f"Wrote file: {abs_path} ({len(data)} bytes)")
            return True
        
        except Exception as e:
            logger.error(f"Error writing file {path}: {str(e)}")
            return False
    
    def delete_file(self, path):
        """
        Delete a file or directory.
        
        Args:
            path: The path to delete
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert to absolute path
            abs_path = os.path.abspath(path)
            
            # Check if the path exists
            if not os.path.exists(abs_path):
                logger.error(f"Path does not exist: {abs_path}")
                return False
            
            # Delete the file or directory
            if os.path.isdir(abs_path):
                shutil.rmtree(abs_path)
                logger.info(f"Deleted directory: {abs_path}")
            else:
                os.remove(abs_path)
                logger.info(f"Deleted file: {abs_path}")
            
            return True
        
        except Exception as e:
            logger.error(f"Error deleting {path}: {str(e)}")
            return False
    
    def move_file(self, src_path, dst_path):
        """
        Move a file or directory.
        
        Args:
            src_path: The source path
            dst_path: The destination path
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert to absolute paths
            abs_src = os.path.abspath(src_path)
            abs_dst = os.path.abspath(dst_path)
            
            # Check if the source path exists
            if not os.path.exists(abs_src):
                logger.error(f"Source path does not exist: {abs_src}")
                return False
            
            # Create destination directory if it doesn't exist
            os.makedirs(os.path.dirname(abs_dst), exist_ok=True)
            
            # Move the file or directory
            shutil.move(abs_src, abs_dst)
            
            logger.info(f"Moved: {abs_src} -> {abs_dst}")
            return True
        
        except Exception as e:
            logger.error(f"Error moving {src_path} to {dst_path}: {str(e)}")
            return False
    
    def copy_file(self, src_path, dst_path):
        """
        Copy a file or directory.
        
        Args:
            src_path: The source path
            dst_path: The destination path
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Convert to absolute paths
            abs_src = os.path.abspath(src_path)
            abs_dst = os.path.abspath(dst_path)
            
            # Check if the source path exists
            if not os.path.exists(abs_src):
                logger.error(f"Source path does not exist: {abs_src}")
                return False
            
            # Create destination directory if it doesn't exist
            os.makedirs(os.path.dirname(abs_dst), exist_ok=True)
            
            # Copy the file or directory
            if os.path.isdir(abs_src):
                shutil.copytree(abs_src, abs_dst)
                logger.info(f"Copied directory: {abs_src} -> {abs_dst}")
            else:
                shutil.copy2(abs_src, abs_dst)
                logger.info(f"Copied file: {abs_src} -> {abs_dst}")
            
            return True
        
        except Exception as e:
            logger.error(f"Error copying {src_path} to {dst_path}: {str(e)}")
            return False
