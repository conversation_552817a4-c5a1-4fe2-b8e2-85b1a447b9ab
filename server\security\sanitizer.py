"""
PepeRAT Security Module - Input Sanitization and Validation
Prevents command injection, RCE, and other security vulnerabilities
"""

import re
import os
import logging
import subprocess
import shlex
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

logger = logging.getLogger(__name__)

class SecuritySanitizer:
    """<PERSON>les input sanitization and validation for PepeRAT"""
    
    def __init__(self):
        # Dangerous command patterns
        self.dangerous_patterns = [
            r'[;&|`$(){}[\]<>]',  # Shell metacharacters
            r'\.\./',  # Directory traversal
            r'\\\.\\',  # Windows directory traversal
            r'eval\s*\(',  # Code evaluation
            r'exec\s*\(',  # Code execution
            r'import\s+os',  # OS module import
            r'__import__',  # Dynamic imports
            r'subprocess',  # Subprocess calls
            r'system\s*\(',  # System calls
            r'popen\s*\(',  # Process opening
            r'rm\s+-rf',  # Dangerous file operations
            r'del\s+/[sqf]',  # Windows dangerous delete
            r'format\s+[c-z]:',  # Disk formatting
            r'shutdown',  # System shutdown
            r'reboot',  # System reboot
            r'halt',  # System halt
            r'poweroff',  # System poweroff
        ]
        
        # Allowed file extensions for uploads
        self.allowed_extensions = {
            '.txt', '.log', '.json', '.xml', '.csv', '.md',
            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp',
            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
            '.zip', '.rar', '.7z', '.tar', '.gz'
        }
        
        # Maximum file size (50MB)
        self.max_file_size = 50 * 1024 * 1024
        
        # Allowed command actions
        self.allowed_actions = {
            'get_screenshot', 'list_directory', 'download_file',
            'upload_file', 'delete_file', 'get_system_info',
            'ping', 'get_clipboard', 'set_clipboard',
            'get_webcam_frame', 'start_microphone',
            'stop_microphone', 'mouse_click', 'mouse_move',
            'key_press', 'key_release', 'type_text'
        }
    
    def sanitize_string(self, input_str: str, max_length: int = 1000) -> str:
        """Sanitize string input"""
        if not isinstance(input_str, str):
            raise ValueError("Input must be a string")
        
        # Limit length
        if len(input_str) > max_length:
            logger.warning(f"Input string truncated from {len(input_str)} to {max_length} characters")
            input_str = input_str[:max_length]
        
        # Remove null bytes
        input_str = input_str.replace('\x00', '')
        
        # Check for dangerous patterns
        for pattern in self.dangerous_patterns:
            if re.search(pattern, input_str, re.IGNORECASE):
                logger.warning(f"Dangerous pattern detected: {pattern}")
                raise ValueError(f"Input contains dangerous pattern: {pattern}")
        
        return input_str
    
    def sanitize_path(self, path: str) -> str:
        """Sanitize file path to prevent directory traversal"""
        if not isinstance(path, str):
            raise ValueError("Path must be a string")
        
        # Remove null bytes
        path = path.replace('\x00', '')
        
        # Normalize path
        path = os.path.normpath(path)
        
        # Check for directory traversal
        if '..' in path or path.startswith('/') or (len(path) > 1 and path[1] == ':' and path[0].isalpha()):
            logger.warning(f"Directory traversal attempt detected: {path}")
            raise ValueError("Invalid path: directory traversal detected")
        
        # Ensure path is relative and safe
        safe_path = os.path.join('.', path.lstrip('./\\'))
        
        return safe_path
    
    def validate_file_upload(self, filename: str, file_size: int, file_content: bytes = None) -> bool:
        """Validate file upload parameters"""
        try:
            # Sanitize filename
            filename = self.sanitize_string(filename, 255)
            
            # Check file extension
            file_ext = Path(filename).suffix.lower()
            if file_ext not in self.allowed_extensions:
                logger.warning(f"Disallowed file extension: {file_ext}")
                raise ValueError(f"File extension not allowed: {file_ext}")
            
            # Check file size
            if file_size > self.max_file_size:
                logger.warning(f"File too large: {file_size} bytes")
                raise ValueError(f"File too large: {file_size} bytes (max: {self.max_file_size})")
            
            # Basic content validation if provided
            if file_content:
                # Check for executable signatures
                dangerous_signatures = [
                    b'\x4d\x5a',  # PE executable (MZ)
                    b'\x7f\x45\x4c\x46',  # ELF executable
                    b'\xca\xfe\xba\xbe',  # Mach-O executable
                    b'\xfe\xed\xfa\xce',  # Mach-O executable (reverse)
                ]
                
                for sig in dangerous_signatures:
                    if file_content.startswith(sig):
                        logger.warning(f"Executable file detected: {filename}")
                        raise ValueError("Executable files are not allowed")
            
            return True
            
        except Exception as e:
            logger.error(f"File validation failed: {str(e)}")
            raise
    
    def validate_command(self, action: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Validate and sanitize command parameters"""
        try:
            # Validate action
            if action not in self.allowed_actions:
                logger.warning(f"Disallowed action: {action}")
                raise ValueError(f"Action not allowed: {action}")
            
            # Sanitize parameters based on action
            sanitized_params = {}
            
            if action in ['list_directory', 'download_file', 'delete_file']:
                if 'path' in params:
                    sanitized_params['path'] = self.sanitize_path(params['path'])
            
            elif action == 'upload_file':
                if 'path' in params:
                    sanitized_params['path'] = self.sanitize_path(params['path'])
                if 'content' in params:
                    # Validate base64 content
                    import base64
                    try:
                        decoded_content = base64.b64decode(params['content'])
                        self.validate_file_upload(
                            sanitized_params.get('path', 'unknown'),
                            len(decoded_content),
                            decoded_content
                        )
                        sanitized_params['content'] = params['content']
                    except Exception as e:
                        raise ValueError(f"Invalid file content: {str(e)}")
            
            elif action in ['mouse_click', 'mouse_move']:
                if 'x' in params:
                    sanitized_params['x'] = max(0, min(int(params['x']), 9999))
                if 'y' in params:
                    sanitized_params['y'] = max(0, min(int(params['y']), 9999))
                if 'button' in params:
                    allowed_buttons = ['left', 'right', 'middle']
                    if params['button'] in allowed_buttons:
                        sanitized_params['button'] = params['button']
            
            elif action in ['key_press', 'key_release']:
                if 'key' in params:
                    # Allow only safe keys
                    key = str(params['key'])
                    if len(key) <= 20 and key.isalnum() or key in ['space', 'enter', 'tab', 'shift', 'ctrl', 'alt']:
                        sanitized_params['key'] = key
            
            elif action == 'type_text':
                if 'text' in params:
                    sanitized_params['text'] = self.sanitize_string(params['text'], 1000)
            
            elif action == 'set_clipboard':
                if 'content' in params:
                    sanitized_params['content'] = self.sanitize_string(params['content'], 10000)
            
            elif action == 'get_screenshot':
                if 'quality' in params:
                    sanitized_params['quality'] = max(10, min(int(params['quality']), 100))
            
            # Copy other safe parameters
            for key, value in params.items():
                if key not in sanitized_params and isinstance(value, (str, int, float, bool)):
                    if isinstance(value, str):
                        sanitized_params[key] = self.sanitize_string(value, 500)
                    else:
                        sanitized_params[key] = value
            
            return sanitized_params
            
        except Exception as e:
            logger.error(f"Command validation failed: {str(e)}")
            raise
    
    def safe_execute_command(self, command: List[str], timeout: int = 30) -> Dict[str, Any]:
        """Safely execute system command with restrictions"""
        try:
            # Validate command components
            if not command or not isinstance(command, list):
                raise ValueError("Command must be a non-empty list")
            
            # Check each component
            for component in command:
                if not isinstance(component, str):
                    raise ValueError("All command components must be strings")
                
                # Check for dangerous patterns
                for pattern in self.dangerous_patterns:
                    if re.search(pattern, component, re.IGNORECASE):
                        raise ValueError(f"Dangerous pattern in command: {pattern}")
            
            # Execute with restrictions
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                timeout=timeout,
                shell=False,  # Never use shell=True
                cwd=None,  # Don't change directory
                env={'PATH': os.environ.get('PATH', '')},  # Minimal environment
            )
            
            return {
                'stdout': result.stdout[:10000],  # Limit output size
                'stderr': result.stderr[:10000],
                'returncode': result.returncode,
                'success': result.returncode == 0
            }
            
        except subprocess.TimeoutExpired:
            logger.warning(f"Command timeout: {command}")
            raise ValueError("Command execution timeout")
        except Exception as e:
            logger.error(f"Command execution failed: {str(e)}")
            raise

# Global sanitizer instance
security_sanitizer = SecuritySanitizer()
