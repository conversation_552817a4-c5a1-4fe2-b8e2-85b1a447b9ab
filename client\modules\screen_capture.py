import os
import platform
import logging
import threading
import time
import base64
from PIL import Image
from io import BytesIO
from typing import Callable, Optional
import mss
import mss.tools

logger = logging.getLogger(__name__)

class ScreenCapture:
    def __init__(self):
        """Initialize the screen capture module."""
        self.sct = mss.mss()

        # Streaming properties
        self.streaming = False
        self.stream_thread = None
        self.stream_callback = None
        self.stream_interval = 1.0
        self.stream_quality = 70
        self.last_frame = None
        self.frame_lock = threading.Lock()

        logger.info("Screen capture module initialized")
    
    def capture(self, monitor_num=1):
        """
        Capture the screen of the specified monitor.
        
        Args:
            monitor_num: The monitor number to capture (1-based index)
        
        Returns:
            PIL.Image: The captured screenshot
        """
        try:
            # Get monitor information
            if monitor_num > len(self.sct.monitors) - 1:
                monitor_num = 1  # Default to the main monitor if specified monitor doesn't exist
            
            # Capture the screen
            monitor = self.sct.monitors[monitor_num]
            sct_img = self.sct.grab(monitor)
            
            # Convert to PIL Image
            img = Image.frombytes("RGB", sct_img.size, sct_img.bgra, "raw", "BGRX")
            
            logger.debug(f"Captured screen with dimensions: {img.size}")
            return img
        
        except Exception as e:
            logger.error(f"Error capturing screen: {str(e)}")
            # Return a blank image in case of error
            return Image.new('RGB', (800, 600), color='black')
    
    def capture_region(self, left, top, width, height):
        """
        Capture a specific region of the screen.
        
        Args:
            left: Left coordinate
            top: Top coordinate
            width: Width of the region
            height: Height of the region
        
        Returns:
            PIL.Image: The captured region
        """
        try:
            # Define the region to capture
            region = {
                "left": left,
                "top": top,
                "width": width,
                "height": height
            }
            
            # Capture the region
            sct_img = self.sct.grab(region)
            
            # Convert to PIL Image
            img = Image.frombytes("RGB", sct_img.size, sct_img.bgra, "raw", "BGRX")
            
            logger.debug(f"Captured region with dimensions: {img.size}")
            return img
        
        except Exception as e:
            logger.error(f"Error capturing screen region: {str(e)}")
            # Return a blank image in case of error
            return Image.new('RGB', (width, height), color='black')
    
    def get_monitors(self):
        """
        Get information about available monitors.

        Returns:
            list: List of monitor information
        """
        try:
            monitors = []
            for i, monitor in enumerate(self.sct.monitors):
                if i == 0:  # Skip the "All in One" monitor
                    continue
                monitors.append({
                    "id": i,
                    "left": monitor["left"],
                    "top": monitor["top"],
                    "width": monitor["width"],
                    "height": monitor["height"]
                })

            logger.info(f"Found {len(monitors)} monitors")
            return monitors

        except Exception as e:
            logger.error(f"Error getting monitor info: {str(e)}")
            return [{"id": 1, "left": 0, "top": 0, "width": 1920, "height": 1080}]

    def capture_to_bytes(self, monitor_num=1, format="JPEG", quality=85):
        """
        Capture screenshot and return as bytes.

        Args:
            monitor_num (int): Monitor number
            format (str): Image format (JPEG, PNG)
            quality (int): JPEG quality (1-100)

        Returns:
            bytes: Image data
        """
        try:
            img = self.capture(monitor_num)

            # Convert to bytes
            buffer = BytesIO()
            if format.upper() == "JPEG":
                img.save(buffer, format="JPEG", quality=quality)
            else:
                img.save(buffer, format=format)

            return buffer.getvalue()

        except Exception as e:
            logger.error(f"Error converting screenshot to bytes: {str(e)}")
            return b""

    def start_streaming(self, callback: Callable, interval: float = 1.0, quality: int = 70, monitor: int = 1):
        """
        Start continuous screen streaming.

        Args:
            callback: Function to call with each frame
            interval: Time between frames in seconds
            quality: JPEG quality (1-100)
            monitor: Monitor number to capture
        """
        if self.streaming:
            self.stop_streaming()

        self.stream_callback = callback
        self.stream_interval = interval
        self.stream_quality = quality
        self.streaming = True

        self.stream_thread = threading.Thread(
            target=self._stream_worker,
            args=(monitor,),
            daemon=True
        )
        self.stream_thread.start()

        logger.info(f"Started screen streaming at {1/interval:.1f} FPS")

    def stop_streaming(self):
        """Stop screen streaming."""
        self.streaming = False
        if self.stream_thread:
            self.stream_thread.join(timeout=2)
            self.stream_thread = None

        logger.info("Stopped screen streaming")

    def _stream_worker(self, monitor: int):
        """Worker thread for continuous streaming."""
        while self.streaming:
            try:
                start_time = time.time()

                # Capture frame
                img = self.capture(monitor)

                # Convert to base64 JPEG
                buffer = BytesIO()
                img.save(buffer, format="JPEG", quality=self.stream_quality)
                img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')

                # Store latest frame
                with self.frame_lock:
                    self.last_frame = {
                        "image": img_data,
                        "width": img.width,
                        "height": img.height,
                        "format": "jpeg",
                        "encoding": "base64",
                        "timestamp": time.time()
                    }

                # Call callback if provided
                if self.stream_callback:
                    self.stream_callback(self.last_frame)

                # Maintain frame rate
                elapsed = time.time() - start_time
                sleep_time = max(0, self.stream_interval - elapsed)
                if sleep_time > 0:
                    time.sleep(sleep_time)

            except Exception as e:
                logger.error(f"Error in stream worker: {str(e)}")
                break

    def get_latest_frame(self) -> Optional[dict]:
        """Get the latest captured frame."""
        with self.frame_lock:
            return self.last_frame.copy() if self.last_frame else None

    def update_stream_settings(self, interval: float = None, quality: int = None):
        """Update streaming settings without stopping."""
        if interval is not None:
            self.stream_interval = interval
            logger.info(f"Updated stream interval to {interval}s")

        if quality is not None:
            self.stream_quality = quality
            logger.info(f"Updated stream quality to {quality}")

    def is_streaming(self) -> bool:
        """Check if currently streaming."""
        return self.streaming

    def get_stream_stats(self) -> dict:
        """Get streaming statistics."""
        with self.frame_lock:
            return {
                "streaming": self.streaming,
                "interval": self.stream_interval,
                "quality": self.stream_quality,
                "fps": 1.0 / self.stream_interval if self.stream_interval > 0 else 0,
                "has_frame": self.last_frame is not None,
                "last_frame_time": self.last_frame.get("timestamp") if self.last_frame else None
            }

    def close(self):
        """Close the screen capture resources."""
        self.stop_streaming()
        self.sct.close()
        logger.info("Screen capture resources released")
