# PepeRAT Server Requirements v2.0

# Core web framework and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0

# Template engine and static files
jinja2==3.1.2
aiofiles==23.2.1

# Security and authentication
passlib[bcrypt]==1.7.4
bcrypt==4.0.1
python-multipart==0.0.6
python-jose[cryptography]==3.3.0

# Cryptography and security
cryptography==41.0.7
pycryptodome==3.19.0

# HTTP client for external requests
httpx==0.25.2
requests==2.31.0

# Environment and configuration
python-dotenv==1.0.0

# Process management and system info
psutil==5.9.6

# Date and time handling
python-dateutil==2.8.2

# JSON handling and validation
pydantic==2.5.0

# Client builder dependencies
pyinstaller==6.2.0
nuitka==1.8.4

# Additional utilities
click==8.1.7
rich==13.7.0

# Development and testing (optional)
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
