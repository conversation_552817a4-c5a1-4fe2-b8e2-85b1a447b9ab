import os
import sys
import platform
import socket
import psutil
import uuid
import logging
import subprocess
import json
from datetime import datetime
from typing import Dict, List, Optional

logger = logging.getLogger(__name__)

def get_system_info():
    """
    Get detailed system information.

    Returns:
        dict: A dictionary containing system information
    """
    try:
        # Basic system information
        info = {
            "hostname": socket.gethostname(),
            "platform": {
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "architecture": platform.architecture()[0],
                "python_version": platform.python_version()
            },
            "network": {
                "ip_address": get_ip_address(),
                "mac_address": get_mac_address()
            },
            "user": {
                "username": get_username(),
                "home_dir": os.path.expanduser("~")
            },
            "timestamp": datetime.now().isoformat()
        }

        # Hardware information
        info["hardware"] = {
            "cpu": {
                "cores_physical": psutil.cpu_count(logical=False),
                "cores_logical": psutil.cpu_count(logical=True),
                "usage_percent": psutil.cpu_percent(interval=1)
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "used": psutil.virtual_memory().used,
                "percent": psutil.virtual_memory().percent
            },
            "disk": get_disk_info()
        }

        # Process information
        info["process"] = {
            "pid": os.getpid(),
            "parent_pid": os.getppid(),
            "cwd": os.getcwd()
        }

        logger.info("Collected system information")
        return info

    except Exception as e:
        logger.error(f"Error collecting system information: {str(e)}")
        # Return basic info if detailed collection fails
        return {
            "hostname": socket.gethostname(),
            "platform": platform.system(),
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def get_ip_address():
    """Get the primary IP address of the machine."""
    try:
        # Create a socket connection to determine the primary interface
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"  # Fallback to localhost

def get_mac_address():
    """Get the MAC address of the machine."""
    try:
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                        for elements in range(0, 2*6, 8)][::-1])
        return mac
    except:
        return "00:00:00:00:00:00"  # Fallback

def get_username():
    """Get the current username."""
    try:
        import getpass
        return getpass.getuser()
    except:
        return os.environ.get("USER", os.environ.get("USERNAME", "unknown"))

def get_disk_info():
    """Get information about disk usage."""
    disks = []
    for partition in psutil.disk_partitions():
        try:
            usage = psutil.disk_usage(partition.mountpoint)
            disk_info = {
                "device": partition.device,
                "mountpoint": partition.mountpoint,
                "fstype": partition.fstype,
                "total": usage.total,
                "used": usage.used,
                "free": usage.free,
                "percent": usage.percent
            }
            disks.append(disk_info)
        except:
            # Some mountpoints may not be accessible
            pass

    return disks

def get_installed_programs() -> List[Dict]:
    """Get list of installed programs (basic detection)"""
    programs = []
    system = platform.system().lower()

    try:
        if system == "windows":
            # Windows - check registry and common paths
            programs.extend(_get_windows_programs())
        elif system == "linux":
            # Linux - check package managers
            programs.extend(_get_linux_programs())
        elif system == "darwin":
            # macOS - check Applications folder
            programs.extend(_get_macos_programs())
    except Exception as e:
        logger.error(f"Error getting installed programs: {str(e)}")

    return programs[:50]  # Limit to 50 programs

def _get_windows_programs() -> List[Dict]:
    """Get Windows installed programs"""
    programs = []
    try:
        import winreg

        # Check common registry locations
        registry_paths = [
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
        ]

        for hkey, path in registry_paths:
            try:
                with winreg.OpenKey(hkey, path) as key:
                    for i in range(winreg.QueryInfoKey(key)[0]):
                        try:
                            subkey_name = winreg.EnumKey(key, i)
                            with winreg.OpenKey(key, subkey_name) as subkey:
                                try:
                                    name = winreg.QueryValueEx(subkey, "DisplayName")[0]
                                    version = winreg.QueryValueEx(subkey, "DisplayVersion")[0]
                                    programs.append({"name": name, "version": version})
                                except FileNotFoundError:
                                    pass
                        except:
                            continue
            except:
                continue

    except ImportError:
        pass

    return programs

def _get_linux_programs() -> List[Dict]:
    """Get Linux installed programs"""
    programs = []

    # Try different package managers
    package_managers = [
        ("dpkg", ["dpkg", "-l"]),
        ("rpm", ["rpm", "-qa"]),
        ("pacman", ["pacman", "-Q"]),
    ]

    for pm_name, cmd in package_managers:
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines[:20]:  # Limit output
                    if pm_name == "dpkg" and line.startswith("ii"):
                        parts = line.split()
                        if len(parts) >= 3:
                            programs.append({"name": parts[1], "version": parts[2]})
                    elif pm_name in ["rpm", "pacman"]:
                        if '-' in line:
                            name_version = line.rsplit('-', 2)
                            if len(name_version) >= 2:
                                programs.append({"name": name_version[0], "version": name_version[1]})
                break
        except:
            continue

    return programs

def _get_macos_programs() -> List[Dict]:
    """Get macOS installed programs"""
    programs = []

    try:
        apps_dir = "/Applications"
        if os.path.exists(apps_dir):
            for item in os.listdir(apps_dir)[:20]:  # Limit to 20
                if item.endswith('.app'):
                    app_name = item.replace('.app', '')
                    programs.append({"name": app_name, "version": "unknown"})
    except:
        pass

    return programs

def get_security_info() -> Dict:
    """Get security-related information"""
    security_info = {
        "antivirus": [],
        "firewall": {"enabled": False, "name": "unknown"},
        "admin_privileges": False,
        "uac_enabled": False  # Windows only
    }

    system = platform.system().lower()

    try:
        # Check admin privileges
        security_info["admin_privileges"] = _check_admin_privileges()

        if system == "windows":
            security_info.update(_get_windows_security())
        elif system == "linux":
            security_info.update(_get_linux_security())
        elif system == "darwin":
            security_info.update(_get_macos_security())

    except Exception as e:
        logger.error(f"Error getting security info: {str(e)}")

    return security_info

def _check_admin_privileges() -> bool:
    """Check if running with admin/root privileges"""
    try:
        if platform.system().lower() == "windows":
            import ctypes
            return ctypes.windll.shell32.IsUserAnAdmin() != 0
        else:
            return os.geteuid() == 0
    except:
        return False

def _get_windows_security() -> Dict:
    """Get Windows-specific security information"""
    info = {}

    try:
        # Check Windows Defender
        result = subprocess.run(
            ["powershell", "-Command", "Get-MpComputerStatus | Select-Object AntivirusEnabled, RealTimeProtectionEnabled"],
            capture_output=True, text=True, timeout=10
        )
        if result.returncode == 0 and "True" in result.stdout:
            info["antivirus"] = [{"name": "Windows Defender", "enabled": True}]

        # Check UAC
        try:
            import winreg
            with winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE,
                              r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System") as key:
                uac_value = winreg.QueryValueEx(key, "EnableLUA")[0]
                info["uac_enabled"] = bool(uac_value)
        except:
            pass

    except:
        pass

    return info

def _get_linux_security() -> Dict:
    """Get Linux-specific security information"""
    info = {}

    try:
        # Check for common firewalls
        firewalls = ["ufw", "iptables", "firewalld"]
        for fw in firewalls:
            try:
                result = subprocess.run(["which", fw], capture_output=True, timeout=5)
                if result.returncode == 0:
                    info["firewall"] = {"enabled": True, "name": fw}
                    break
            except:
                continue
    except:
        pass

    return info

def _get_macos_security() -> Dict:
    """Get macOS-specific security information"""
    info = {}

    try:
        # Check firewall status
        result = subprocess.run(
            ["defaults", "read", "/Library/Preferences/com.apple.alf", "globalstate"],
            capture_output=True, text=True, timeout=5
        )
        if result.returncode == 0:
            firewall_state = result.stdout.strip()
            info["firewall"] = {"enabled": firewall_state != "0", "name": "macOS Firewall"}
    except:
        pass

    return info

def get_location_info() -> Dict:
    """Get approximate location information"""
    location_info = {
        "country": "unknown",
        "region": "unknown",
        "city": "unknown",
        "timezone": "unknown",
        "public_ip": "unknown"
    }

    try:
        # Get public IP and location from ipapi.co
        import requests

        # Get public IP
        try:
            ip_response = requests.get("https://api.ipify.org", timeout=5)
            if ip_response.status_code == 200:
                location_info["public_ip"] = ip_response.text.strip()
        except:
            pass

        # Get location info
        try:
            location_response = requests.get("https://ipapi.co/json/", timeout=10)
            if location_response.status_code == 200:
                data = location_response.json()
                location_info.update({
                    "country": data.get("country_name", "unknown"),
                    "region": data.get("region", "unknown"),
                    "city": data.get("city", "unknown"),
                    "timezone": data.get("timezone", "unknown")
                })
        except:
            pass

        # Get local timezone as fallback
        try:
            import time
            location_info["local_timezone"] = time.tzname[0]
        except:
            pass

    except Exception as e:
        logger.error(f"Error getting location info: {str(e)}")

    return location_info
