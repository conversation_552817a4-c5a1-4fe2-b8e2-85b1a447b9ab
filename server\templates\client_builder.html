<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PepeRAT Client Builder</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-green-400">
                <i class="fas fa-hammer mr-2"></i>
                PepeRAT Client Builder
            </h1>
            <div class="flex space-x-4">
                <a href="/dashboard" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                    <i class="fas fa-dashboard mr-2"></i>Dashboard
                </a>
                <span class="text-gray-400">Welcome, {{ username }}</span>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Configuration Panel -->
            <div class="lg:col-span-2">
                <div class="bg-gray-800 rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-6 text-green-400">
                        <i class="fas fa-cog mr-2"></i>Client Configuration
                    </h2>

                    <form id="clientConfigForm" class="space-y-6">
                        <!-- Basic Settings -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-lg font-medium mb-4 text-blue-400">Basic Settings</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Build Name</label>
                                    <input type="text" id="buildName" name="buildName" 
                                           class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-green-400"
                                           value="peperat_client" required>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium mb-2">Platform</label>
                                    <select id="platform" name="platform" 
                                            class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-green-400">
                                        {% for key, platform in platforms.items() %}
                                        <option value="{{ key }}">{{ platform.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium mb-2">Server URL</label>
                                    <input type="text" id="serverUrl" name="serverUrl" 
                                           class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-green-400"
                                           value="{{ default_config.server_url }}" required>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium mb-2">Client Name</label>
                                    <input type="text" id="clientName" name="clientName" 
                                           class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-green-400"
                                           value="{{ default_config.client_name }}">
                                </div>
                            </div>
                        </div>

                        <!-- Connection Settings -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-lg font-medium mb-4 text-blue-400">Connection Settings</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div>
                                    <label class="block text-sm font-medium mb-2">Reconnect Delay (seconds)</label>
                                    <input type="number" id="reconnectDelay" name="reconnectDelay" 
                                           class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-green-400"
                                           value="{{ default_config.reconnect_delay }}" min="1" max="300">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium mb-2">Screen Capture Interval (seconds)</label>
                                    <input type="number" id="screenInterval" name="screenInterval" step="0.1"
                                           class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-green-400"
                                           value="{{ default_config.screen_capture_interval }}" min="0.5" max="60">
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium mb-2">Heartbeat Interval (seconds)</label>
                                    <input type="number" id="heartbeatInterval" name="heartbeatInterval" 
                                           class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-green-400"
                                           value="{{ default_config.heartbeat_interval }}" min="10" max="300">
                                </div>
                            </div>
                        </div>

                        <!-- Security Settings -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-lg font-medium mb-4 text-blue-400">Security Settings</h3>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="flex items-center">
                                    <input type="checkbox" id="encryptionEnabled" name="encryptionEnabled" 
                                           class="mr-2 w-4 h-4 text-green-600 bg-gray-600 border-gray-500 rounded focus:ring-green-500"
                                           {% if default_config.encryption_enabled %}checked{% endif %}>
                                    <label for="encryptionEnabled" class="text-sm font-medium">Enable Encryption</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="stealthMode" name="stealthMode" 
                                           class="mr-2 w-4 h-4 text-green-600 bg-gray-600 border-gray-500 rounded focus:ring-green-500"
                                           {% if default_config.stealth_mode %}checked{% endif %}>
                                    <label for="stealthMode" class="text-sm font-medium">Stealth Mode</label>
                                </div>
                                
                                <div class="flex items-center">
                                    <input type="checkbox" id="autoStart" name="autoStart" 
                                           class="mr-2 w-4 h-4 text-green-600 bg-gray-600 border-gray-500 rounded focus:ring-green-500"
                                           {% if default_config.auto_start %}checked{% endif %}>
                                    <label for="autoStart" class="text-sm font-medium">Auto Start</label>
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium mb-2">Persistence Method</label>
                                    <select id="persistenceMethod" name="persistenceMethod" 
                                            class="w-full bg-gray-600 border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-green-400">
                                        <option value="auto">Auto</option>
                                        <option value="registry">Registry (Windows)</option>
                                        <option value="startup">Startup Folder (Windows)</option>
                                        <option value="systemd">Systemd (Linux)</option>
                                        <option value="crontab">Crontab (Linux)</option>
                                        <option value="launchd">LaunchAgents (macOS)</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Features -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-lg font-medium mb-4 text-blue-400">Features</h3>
                            
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                {% for feature, enabled in default_config.features.items() %}
                                <div class="flex items-center">
                                    <input type="checkbox" id="feature_{{ feature }}" name="feature_{{ feature }}" 
                                           class="mr-2 w-4 h-4 text-green-600 bg-gray-600 border-gray-500 rounded focus:ring-green-500"
                                           {% if enabled %}checked{% endif %}>
                                    <label for="feature_{{ feature }}" class="text-sm font-medium capitalize">
                                        {{ feature.replace('_', ' ') }}
                                    </label>
                                </div>
                                {% endfor %}
                            </div>
                        </div>

                        <!-- Build Options -->
                        <div class="bg-gray-700 rounded-lg p-4">
                            <h3 class="text-lg font-medium mb-4 text-blue-400">Build Options</h3>
                            
                            <div class="flex items-center space-x-6">
                                <div class="flex items-center">
                                    <input type="checkbox" id="compileExecutable" name="compileExecutable" 
                                           class="mr-2 w-4 h-4 text-green-600 bg-gray-600 border-gray-500 rounded focus:ring-green-500">
                                    <label for="compileExecutable" class="text-sm font-medium">Compile to Executable</label>
                                </div>
                            </div>
                        </div>

                        <!-- Build Button -->
                        <div class="flex justify-center">
                            <button type="submit" id="buildButton" 
                                    class="bg-green-600 hover:bg-green-700 px-8 py-3 rounded-lg font-semibold text-lg">
                                <i class="fas fa-hammer mr-2"></i>Build Client
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Build History -->
            <div class="lg:col-span-1">
                <div class="bg-gray-800 rounded-lg p-6">
                    <h2 class="text-xl font-semibold mb-6 text-green-400">
                        <i class="fas fa-history mr-2"></i>Build History
                    </h2>
                    
                    <div id="buildHistory" class="space-y-4">
                        <!-- Build history will be loaded here -->
                    </div>
                    
                    <button id="refreshHistory" class="w-full mt-4 bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                        <i class="fas fa-refresh mr-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- Build Status Modal -->
        <div id="buildModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
            <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                <h3 class="text-lg font-semibold mb-4 text-green-400">Build Status</h3>
                <div id="buildStatus" class="mb-4">
                    <div class="flex items-center">
                        <i class="fas fa-spinner fa-spin mr-2 text-blue-400"></i>
                        <span>Building client...</span>
                    </div>
                </div>
                <div id="buildActions" class="hidden">
                    <button id="downloadBuild" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded mr-2">
                        <i class="fas fa-download mr-2"></i>Download
                    </button>
                    <button id="closeBuildModal" class="bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Client builder JavaScript will be added here
        let currentBuildId = null;

        // Form submission
        document.getElementById('clientConfigForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await buildClient();
        });

        // Build client function
        async function buildClient() {
            const buildButton = document.getElementById('buildButton');
            const buildModal = document.getElementById('buildModal');
            const buildStatus = document.getElementById('buildStatus');
            const buildActions = document.getElementById('buildActions');

            // Show modal
            buildModal.classList.remove('hidden');
            buildModal.classList.add('flex');
            buildActions.classList.add('hidden');

            // Disable build button
            buildButton.disabled = true;
            buildButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Building...';

            try {
                // Collect form data
                const formData = new FormData(document.getElementById('clientConfigForm'));
                const config = {
                    server_url: formData.get('serverUrl'),
                    client_name: formData.get('clientName'),
                    reconnect_delay: parseInt(formData.get('reconnectDelay')),
                    screen_capture_interval: parseFloat(formData.get('screenInterval')),
                    heartbeat_interval: parseInt(formData.get('heartbeatInterval')),
                    encryption_enabled: formData.get('encryptionEnabled') === 'on',
                    stealth_mode: formData.get('stealthMode') === 'on',
                    auto_start: formData.get('autoStart') === 'on',
                    persistence_method: formData.get('persistenceMethod'),
                    features: {}
                };

                // Collect features
                const features = ['screen_capture', 'file_manager', 'clipboard', 'input_control', 'webcam', 'microphone', 'registry', 'persistence'];
                features.forEach(feature => {
                    config.features[feature] = formData.get(`feature_${feature}`) === 'on';
                });

                const buildRequest = {
                    config: config,
                    platform: formData.get('platform'),
                    build_name: formData.get('buildName'),
                    compile_executable: formData.get('compileExecutable') === 'on'
                };

                // Send build request
                const response = await fetch('/api/builder/build', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(buildRequest)
                });

                const result = await response.json();

                if (result.success) {
                    currentBuildId = result.build_id;
                    buildStatus.innerHTML = `
                        <div class="text-green-400">
                            <i class="fas fa-check-circle mr-2"></i>
                            Build completed successfully!
                        </div>
                        <div class="text-sm text-gray-400 mt-2">
                            Build ID: ${result.build_id}
                        </div>
                    `;
                    buildActions.classList.remove('hidden');
                    loadBuildHistory();
                } else {
                    buildStatus.innerHTML = `
                        <div class="text-red-400">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            Build failed: ${result.error}
                        </div>
                    `;
                }
            } catch (error) {
                buildStatus.innerHTML = `
                    <div class="text-red-400">
                        <i class="fas fa-exclamation-circle mr-2"></i>
                        Error: ${error.message}
                    </div>
                `;
            } finally {
                // Re-enable build button
                buildButton.disabled = false;
                buildButton.innerHTML = '<i class="fas fa-hammer mr-2"></i>Build Client';
            }
        }

        // Download build
        document.getElementById('downloadBuild').addEventListener('click', () => {
            if (currentBuildId) {
                window.location.href = `/api/builder/download/${currentBuildId}`;
            }
        });

        // Close modal
        document.getElementById('closeBuildModal').addEventListener('click', () => {
            document.getElementById('buildModal').classList.add('hidden');
            document.getElementById('buildModal').classList.remove('flex');
        });

        // Load build history
        async function loadBuildHistory() {
            try {
                const response = await fetch('/api/builder/builds');
                const data = await response.json();
                
                const historyContainer = document.getElementById('buildHistory');
                historyContainer.innerHTML = '';

                if (data.builds.length === 0) {
                    historyContainer.innerHTML = '<div class="text-gray-400 text-center">No builds yet</div>';
                    return;
                }

                data.builds.forEach(build => {
                    const buildElement = document.createElement('div');
                    buildElement.className = 'bg-gray-700 rounded p-3 text-sm';
                    buildElement.innerHTML = `
                        <div class="font-medium text-green-400">${build.build_id}</div>
                        <div class="text-gray-400">${build.platform}</div>
                        <div class="text-xs text-gray-500 mt-1">${new Date(build.created_at).toLocaleString()}</div>
                        <div class="mt-2 space-x-2">
                            <button onclick="downloadBuild('${build.build_id}')" 
                                    class="bg-blue-600 hover:bg-blue-700 px-2 py-1 rounded text-xs">
                                <i class="fas fa-download"></i>
                            </button>
                            <button onclick="deleteBuild('${build.build_id}')" 
                                    class="bg-red-600 hover:bg-red-700 px-2 py-1 rounded text-xs">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    `;
                    historyContainer.appendChild(buildElement);
                });
            } catch (error) {
                console.error('Error loading build history:', error);
            }
        }

        // Download build from history
        function downloadBuild(buildId) {
            window.location.href = `/api/builder/download/${buildId}`;
        }

        // Delete build
        async function deleteBuild(buildId) {
            if (confirm('Are you sure you want to delete this build?')) {
                try {
                    await fetch(`/api/builder/builds/${buildId}`, { method: 'DELETE' });
                    loadBuildHistory();
                } catch (error) {
                    alert('Error deleting build: ' + error.message);
                }
            }
        }

        // Refresh history
        document.getElementById('refreshHistory').addEventListener('click', loadBuildHistory);

        // Load build history on page load
        loadBuildHistory();
    </script>
</body>
</html>
