/* ZENRAT - Main Stylesheet */

/* ===== Base Styles ===== */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #1abc9c;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --success-color: #2ecc71;
    --dark-color: #1a1a2e;
    --light-color: #f5f6fa;
    --gray-color: #95a5a6;
    --border-color: #34495e;

    --sidebar-width: 250px;
    --header-height: 60px;

    --font-main: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-mono: 'Courier New', Courier, monospace;

    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.3);

    --transition-speed: 0.3s;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-main);
    background-color: #121212;
    color: var(--light-color);
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: var(--secondary-color);
}

ul {
    list-style: none;
}

button, .btn {
    cursor: pointer;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    transition: all var(--transition-speed) ease;
}

input, select, textarea {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: #2a2a3a;
    color: var(--light-color);
    font-size: 14px;
}

input:focus, select:focus, textarea:focus {
    outline: none;
    border-color: var(--secondary-color);
}

/* ===== Utility Classes ===== */
.hidden {
    display: none !important;
}

.btn-primary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
}

.btn-secondary {
    background-color: var(--gray-color);
    color: white;
}

.btn-secondary:hover {
    background-color: #7f8c8d;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #27ae60;
}

.btn-control {
    background-color: #2a2a3a;
    color: var(--light-color);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 6px;
    transition: all var(--transition-speed) ease;
}

.btn-control:hover {
    background-color: var(--secondary-color);
}

.btn-control:disabled {
    background-color: #3a3a4a;
    color: var(--gray-color);
    cursor: not-allowed;
}

.btn-control i {
    font-size: 14px;
}

/* ===== Layout Components ===== */

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width);
    height: 100vh;
    background-color: var(--dark-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    z-index: 100;
}

.sidebar-header {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header h2 {
    color: var(--secondary-color);
    font-size: 24px;
    letter-spacing: 1px;
}

.sidebar-menu {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-menu ul li {
    margin-bottom: 5px;
}

.sidebar-menu ul li a {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    color: var(--light-color);
    transition: all var(--transition-speed) ease;
}

.sidebar-menu ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-menu ul li a:hover {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--secondary-color);
}

.sidebar-menu ul li.active a {
    background-color: rgba(52, 152, 219, 0.2);
    color: var(--secondary-color);
    border-left: 3px solid var(--secondary-color);
}

.sidebar-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    font-size: 12px;
    color: var(--gray-color);
}

/* Main Content */
.main-content {
    margin-left: var(--sidebar-width);
    padding: 20px;
    min-height: 100vh;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.header h1 {
    font-size: 24px;
    color: var(--light-color);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.date-time {
    font-size: 14px;
    color: var(--gray-color);
}

.client-status {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.client-status.online {
    background-color: rgba(46, 204, 113, 0.2);
    color: var(--success-color);
}

.client-status.online::before {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--success-color);
    margin-right: 6px;
}

.client-status.offline {
    background-color: rgba(231, 76, 60, 0.2);
    color: var(--danger-color);
}

.client-status.offline::before {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--danger-color);
    margin-right: 6px;
}

/* ===== Login Page ===== */
.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

.login-container {
    width: 400px;
    background-color: rgba(26, 26, 46, 0.8);
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(52, 152, 219, 0.3);
}

.login-header {
    padding: 30px 20px;
    text-align: center;
    background-color: rgba(52, 152, 219, 0.1);
    border-bottom: 1px solid rgba(52, 152, 219, 0.3);
}

.login-header h1 {
    color: var(--secondary-color);
    font-size: 32px;
    margin-bottom: 5px;
}

.login-header p {
    color: var(--gray-color);
    font-size: 14px;
}

.login-form {
    padding: 30px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--light-color);
}

.form-group input {
    width: 100%;
    padding: 12px;
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(52, 152, 219, 0.3);
    color: var(--light-color);
    border-radius: 4px;
    transition: all var(--transition-speed) ease;
}

.form-group input:focus {
    border-color: var(--secondary-color);
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.btn-login {
    width: 100%;
    padding: 12px;
    background-color: var(--secondary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.btn-login:hover {
    background-color: #2980b9;
}

.error-message {
    background-color: rgba(231, 76, 60, 0.2);
    color: var(--danger-color);
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    font-size: 14px;
    text-align: center;
}

.login-footer {
    padding: 15px;
    text-align: center;
    border-top: 1px solid rgba(52, 152, 219, 0.3);
    font-size: 12px;
    color: var(--gray-color);
}

/* ===== Dashboard Page ===== */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #1e1e30;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-speed) ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
    border-color: var(--secondary-color);
}

.stat-icon {
    width: 50px;
    height: 50px;
    background-color: rgba(52, 152, 219, 0.1);
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 15px;
}

.stat-icon i {
    font-size: 20px;
    color: var(--secondary-color);
}

.stat-info h3 {
    font-size: 14px;
    color: var(--gray-color);
    margin-bottom: 5px;
}

.stat-info p {
    font-size: 24px;
    font-weight: 600;
    color: var(--light-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h2 {
    font-size: 20px;
    color: var(--light-color);
}

.search-box {
    position: relative;
    width: 250px;
}

.search-box input {
    width: 100%;
    padding: 8px 12px 8px 35px;
    border-radius: 20px;
    background-color: #2a2a3a;
    border: 1px solid var(--border-color);
}

.search-box i {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-color);
}

.clients-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.client-card {
    background-color: #1e1e30;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    transition: all var(--transition-speed) ease;
}

.client-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
    border-color: var(--secondary-color);
}

.client-header {
    padding: 15px;
    background-color: #2a2a3a;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.client-header h3 {
    font-size: 16px;
    color: var(--light-color);
}

.client-info {
    padding: 15px;
}

.client-info p {
    margin-bottom: 8px;
    font-size: 14px;
    color: var(--gray-color);
    display: flex;
    align-items: center;
}

.client-info p i {
    width: 20px;
    margin-right: 8px;
    color: var(--secondary-color);
}

.client-actions {
    padding: 15px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
}

.no-clients {
    grid-column: 1 / -1;
    text-align: center;
    padding: 50px 0;
    color: var(--gray-color);
}

.no-clients i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-clients p {
    font-size: 18px;
    margin-bottom: 5px;
}

.no-clients .sub-text {
    font-size: 14px;
    opacity: 0.7;
}

/* ===== Client Control Page ===== */
.client-control-container {
    background-color: #1e1e30;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.control-tabs {
    display: flex;
    background-color: #2a2a3a;
    border-bottom: 1px solid var(--border-color);
}

.tab {
    padding: 15px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--gray-color);
    display: flex;
    align-items: center;
    transition: all var(--transition-speed) ease;
}

.tab i {
    margin-right: 8px;
}

.tab:hover {
    color: var(--light-color);
    background-color: rgba(52, 152, 219, 0.1);
}

.tab.active {
    color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.2);
    border-bottom: 2px solid var(--secondary-color);
}

.tab-content {
    min-height: 500px;
}

.tab-pane {
    display: none;
    padding: 20px;
}

.tab-pane.active {
    display: block;
}

/* Screen Tab */
.screen-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    align-items: center;
    flex-wrap: wrap;
}

.quality-selector, .fps-selector {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quality-selector label, .fps-selector label {
    font-size: 14px;
    color: var(--gray-color);
}

.quality-selector select, .fps-selector select {
    padding: 6px 10px;
    background-color: #2a2a3a;
    border: 1px solid var(--border-color);
    color: var(--light-color);
    border-radius: 4px;
}

.screen-container {
    background-color: #0a0a15;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    height: 500px;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid var(--border-color);
}

.screen-placeholder {
    text-align: center;
    color: var(--gray-color);
}

.screen-placeholder i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.screen-placeholder p {
    font-size: 18px;
    margin-bottom: 5px;
}

.screen-placeholder .sub-text {
    font-size: 14px;
    opacity: 0.7;
}

.screen-image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Files Tab */
.file-explorer {
    display: flex;
    flex-direction: column;
    height: 500px;
}

.file-toolbar {
    display: flex;
    gap: 10px;
    padding: 10px;
    background-color: #2a2a3a;
    border-radius: 4px 4px 0 0;
    border: 1px solid var(--border-color);
    align-items: center;
}

.file-toolbar input[type="text"] {
    flex: 1;
    background-color: #1a1a2e;
}

.file-list-container {
    flex: 1;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 4px 4px;
}

.file-list {
    width: 100%;
    border-collapse: collapse;
}

.file-list th, .file-list td {
    padding: 10px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.file-list th {
    background-color: #2a2a3a;
    color: var(--light-color);
    font-weight: 500;
    font-size: 14px;
}

.file-list td {
    font-size: 14px;
    color: var(--gray-color);
}

.file-list tr:hover td {
    background-color: rgba(52, 152, 219, 0.1);
    color: var(--light-color);
}

.file-list .file-name {
    display: flex;
    align-items: center;
}

.file-list .file-name i {
    margin-right: 8px;
    color: var(--secondary-color);
}

.file-list .file-actions {
    display: flex;
    gap: 5px;
}

.loading-files {
    text-align: center;
    padding: 20px;
    color: var(--gray-color);
}

/* Terminal Tab */
.terminal-container {
    background-color: #0a0a15;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    height: 500px;
    font-family: var(--font-mono);
    border: 1px solid var(--border-color);
}

.terminal-output {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
    color: var(--light-color);
    font-size: 14px;
    line-height: 1.5;
}

.terminal-line {
    margin-bottom: 5px;
    word-break: break-all;
}

.terminal-input-container {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: #1a1a2e;
    border-top: 1px solid var(--border-color);
}

.terminal-prompt {
    color: var(--secondary-color);
    margin-right: 10px;
    font-weight: bold;
}

.terminal-input-container input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--light-color);
    font-family: var(--font-mono);
    font-size: 14px;
}

.terminal-input-container input:focus {
    outline: none;
}

/* System Info Tab */
.system-info-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.system-info-card {
    background-color: #1a1a2e;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid var(--border-color);
}

.system-info-card h3 {
    font-size: 16px;
    color: var(--light-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.system-info-card h3 i {
    color: var(--secondary-color);
}

.info-table {
    width: 100%;
    border-collapse: collapse;
}

.info-table td {
    padding: 8px 0;
    font-size: 14px;
    border-bottom: 1px solid rgba(52, 152, 219, 0.1);
}

.info-table td:first-child {
    color: var(--gray-color);
    width: 40%;
}

.info-table td:last-child {
    color: var(--light-color);
}

.disk-item {
    margin-bottom: 15px;
}

.disk-item p {
    font-size: 14px;
    margin-bottom: 5px;
}

.disk-usage {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
}

.disk-usage-bar {
    flex: 1;
    height: 8px;
    background-color: #2a2a3a;
    border-radius: 4px;
    overflow: hidden;
}

.disk-usage-fill {
    height: 100%;
    background-color: var(--secondary-color);
    border-radius: 4px;
}

.disk-usage span {
    font-size: 12px;
    color: var(--gray-color);
    width: 60px;
    text-align: right;
}

/* ===== Modals ===== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    position: relative;
    background-color: #1e1e30;
    margin: 50px auto;
    width: 80%;
    max-width: 800px;
    border-radius: 8px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 15px 20px;
    background-color: #2a2a3a;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    font-size: 18px;
    color: var(--light-color);
}

.close-modal {
    font-size: 24px;
    color: var(--gray-color);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.close-modal:hover {
    color: var(--danger-color);
}

.modal-body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.modal-footer {
    padding: 15px 20px;
    background-color: #2a2a3a;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* File Preview Modal */
.file-preview-container {
    min-height: 300px;
    max-height: 500px;
    overflow: auto;
    background-color: #0a0a15;
    border-radius: 4px;
    border: 1px solid var(--border-color);
}

#file-preview-text {
    padding: 15px;
    font-family: var(--font-mono);
    font-size: 14px;
    line-height: 1.5;
    color: var(--light-color);
    white-space: pre-wrap;
    word-break: break-all;
}

#file-preview-image {
    max-width: 100%;
    display: block;
    margin: 0 auto;
}

/* File Upload Modal */
.upload-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.upload-dropzone {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.upload-dropzone:hover {
    border-color: var(--secondary-color);
    background-color: rgba(52, 152, 219, 0.05);
}

.upload-dropzone i {
    font-size: 48px;
    color: var(--secondary-color);
    margin-bottom: 15px;
    opacity: 0.7;
}

.upload-dropzone p {
    color: var(--gray-color);
    font-size: 16px;
}

.upload-file-list {
    max-height: 200px;
    overflow-y: auto;
}

.upload-file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #2a2a3a;
    border-radius: 4px;
    margin-bottom: 8px;
}

.upload-file-item .file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.upload-file-item .file-info i {
    color: var(--secondary-color);
}

.upload-file-item .file-name {
    font-size: 14px;
    color: var(--light-color);
}

.upload-file-item .file-size {
    font-size: 12px;
    color: var(--gray-color);
}

.upload-file-item .remove-file {
    color: var(--danger-color);
    cursor: pointer;
}

/* Settings Modal */
.settings-section {
    margin-bottom: 20px;
}

.settings-section h3 {
    font-size: 16px;
    color: var(--light-color);
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--border-color);
}

/* ===== Advanced Tab ===== */
.advanced-controls-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.advanced-section {
    background-color: #1a1a2e;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid var(--border-color);
}

.advanced-section h3 {
    font-size: 16px;
    color: var(--light-color);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.advanced-section h3 i {
    color: var(--secondary-color);
}

.advanced-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
    align-items: center;
}

.public-ip-display {
    background-color: #2a2a3a;
    padding: 8px 12px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: 10px;
}

.public-ip-display .label {
    color: var(--gray-color);
    font-size: 14px;
}

.public-ip-display .value {
    color: var(--light-color);
    font-size: 14px;
    font-weight: 500;
}

.screenshots-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.screenshot-item {
    background-color: #2a2a3a;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--border-color);
    transition: all var(--transition-speed) ease;
}

.screenshot-item:hover {
    transform: scale(1.05);
    border-color: var(--secondary-color);
}

.screenshot-img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    cursor: pointer;
}

.screenshot-info {
    padding: 8px;
    font-size: 12px;
    color: var(--gray-color);
}

.screenshot-actions {
    display: flex;
    justify-content: space-between;
    padding: 8px;
    border-top: 1px solid var(--border-color);
}

.screenshot-actions button {
    background: none;
    border: none;
    color: var(--secondary-color);
    cursor: pointer;
    font-size: 14px;
}

.screenshot-actions button:hover {
    color: var(--light-color);
}

.loading-text {
    grid-column: 1 / -1;
    text-align: center;
    padding: 20px;
    color: var(--gray-color);
}

.run-file-form, .rename-file-form {
    background-color: #2a2a3a;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    color: var(--gray-color);
}

.form-group input {
    width: 100%;
    padding: 8px 12px;
    background-color: #1a1a2e;
    border: 1px solid var(--border-color);
    color: var(--light-color);
    border-radius: 4px;
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
    .sidebar {
        width: 60px;
        overflow: hidden;
    }

    .sidebar-header h2 {
        display: none;
    }

    .sidebar-menu ul li a span {
        display: none;
    }

    .sidebar-menu ul li a i {
        margin-right: 0;
        font-size: 18px;
    }

    .sidebar-footer {
        display: none;
    }

    .main-content {
        margin-left: 60px;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .clients-grid {
        grid-template-columns: 1fr;
    }

    .system-info-container {
        grid-template-columns: 1fr;
    }

    .screenshots-gallery {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
    }
}
