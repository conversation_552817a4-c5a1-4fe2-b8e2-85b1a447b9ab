import os
import platform
import logging
from PIL import Image
import mss
import mss.tools

logger = logging.getLogger(__name__)

class ScreenCapture:
    def __init__(self):
        """Initialize the screen capture module."""
        self.sct = mss.mss()
        logger.info("Screen capture module initialized")
    
    def capture(self, monitor_num=1):
        """
        Capture the screen of the specified monitor.
        
        Args:
            monitor_num: The monitor number to capture (1-based index)
        
        Returns:
            PIL.Image: The captured screenshot
        """
        try:
            # Get monitor information
            if monitor_num > len(self.sct.monitors) - 1:
                monitor_num = 1  # Default to the main monitor if specified monitor doesn't exist
            
            # Capture the screen
            monitor = self.sct.monitors[monitor_num]
            sct_img = self.sct.grab(monitor)
            
            # Convert to PIL Image
            img = Image.frombytes("RGB", sct_img.size, sct_img.bgra, "raw", "BGRX")
            
            logger.debug(f"Captured screen with dimensions: {img.size}")
            return img
        
        except Exception as e:
            logger.error(f"Error capturing screen: {str(e)}")
            # Return a blank image in case of error
            return Image.new('RGB', (800, 600), color='black')
    
    def capture_region(self, left, top, width, height):
        """
        Capture a specific region of the screen.
        
        Args:
            left: Left coordinate
            top: Top coordinate
            width: Width of the region
            height: Height of the region
        
        Returns:
            PIL.Image: The captured region
        """
        try:
            # Define the region to capture
            region = {
                "left": left,
                "top": top,
                "width": width,
                "height": height
            }
            
            # Capture the region
            sct_img = self.sct.grab(region)
            
            # Convert to PIL Image
            img = Image.frombytes("RGB", sct_img.size, sct_img.bgra, "raw", "BGRX")
            
            logger.debug(f"Captured region with dimensions: {img.size}")
            return img
        
        except Exception as e:
            logger.error(f"Error capturing screen region: {str(e)}")
            # Return a blank image in case of error
            return Image.new('RGB', (width, height), color='black')
    
    def close(self):
        """Close the screen capture resources."""
        self.sct.close()
        logger.info("Screen capture resources released")
