import os
import sys
import platform
import socket
import psutil
import uuid
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

def get_system_info():
    """
    Get detailed system information.

    Returns:
        dict: A dictionary containing system information
    """
    try:
        # Basic system information
        info = {
            "hostname": socket.gethostname(),
            "platform": {
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "architecture": platform.architecture()[0],
                "python_version": platform.python_version()
            },
            "network": {
                "ip_address": get_ip_address(),
                "mac_address": get_mac_address()
            },
            "user": {
                "username": get_username(),
                "home_dir": os.path.expanduser("~")
            },
            "timestamp": datetime.now().isoformat()
        }

        # Hardware information
        info["hardware"] = {
            "cpu": {
                "cores_physical": psutil.cpu_count(logical=False),
                "cores_logical": psutil.cpu_count(logical=True),
                "usage_percent": psutil.cpu_percent(interval=1)
            },
            "memory": {
                "total": psutil.virtual_memory().total,
                "available": psutil.virtual_memory().available,
                "used": psutil.virtual_memory().used,
                "percent": psutil.virtual_memory().percent
            },
            "disk": get_disk_info()
        }

        # Process information
        info["process"] = {
            "pid": os.getpid(),
            "parent_pid": os.getppid(),
            "cwd": os.getcwd()
        }

        logger.info("Collected system information")
        return info

    except Exception as e:
        logger.error(f"Error collecting system information: {str(e)}")
        # Return basic info if detailed collection fails
        return {
            "hostname": socket.gethostname(),
            "platform": platform.system(),
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

def get_ip_address():
    """Get the primary IP address of the machine."""
    try:
        # Create a socket connection to determine the primary interface
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"  # Fallback to localhost

def get_mac_address():
    """Get the MAC address of the machine."""
    try:
        mac = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff)
                        for elements in range(0, 2*6, 8)][::-1])
        return mac
    except:
        return "00:00:00:00:00:00"  # Fallback

def get_username():
    """Get the current username."""
    try:
        import getpass
        return getpass.getuser()
    except:
        return os.environ.get("USER", os.environ.get("USERNAME", "unknown"))

def get_disk_info():
    """Get information about disk usage."""
    disks = []
    for partition in psutil.disk_partitions():
        try:
            usage = psutil.disk_usage(partition.mountpoint)
            disk_info = {
                "device": partition.device,
                "mountpoint": partition.mountpoint,
                "fstype": partition.fstype,
                "total": usage.total,
                "used": usage.used,
                "free": usage.free,
                "percent": usage.percent
            }
            disks.append(disk_info)
        except:
            # Some mountpoints may not be accessible
            pass

    return disks
